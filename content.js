// إضافة اسم "med" على فيديوهات YouTube مع إمكانية التحميل
(function() {
    'use strict';

    // دالة لاستخراج معرف الفيديو من الرابط
    function getVideoId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('v');
    }

    // دالة لاستخراج عنوان الفيديو
    function getVideoTitle() {
        const titleElement = document.querySelector('h1.ytd-video-primary-info-renderer yt-formatted-string') ||
                           document.querySelector('h1.title') ||
                           document.querySelector('#container h1');
        return titleElement ? titleElement.textContent.trim() : 'فيديو_يوتوب';
    }

    // دالة لتنظيف اسم الملف
    function sanitizeFileName(fileName) {
        return fileName.replace(/[<>:"/\\|?*]/g, '_').substring(0, 100);
    }

    // دالة لإنشاء عنصر النص
    function createMedElement() {
        const medElement = document.createElement('div');
        medElement.id = 'med-overlay';
        medElement.textContent = 'med';
        medElement.className = 'med-text-overlay';

        // إضافة خصائص إضافية للتأكد من إمكانية النقر
        medElement.style.cssText = `
            position: absolute !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 999999 !important;
            pointer-events: auto !important;
            cursor: pointer !important;
            display: block !important;
        `;

        medElement.title = 'انقر لتحميل الفيديو';

        // إضافة أحداث متعددة للتأكد من عمل النقر
        medElement.addEventListener('click', handleDownloadClick, true);
        medElement.addEventListener('mousedown', handleDownloadClick, true);
        medElement.addEventListener('touchstart', handleDownloadClick, true);

        // منع الأحداث الافتراضية من التداخل
        medElement.addEventListener('click', function(e) {
            e.stopPropagation();
            e.preventDefault();
        }, false);

        return medElement;
    }

    // دالة للتعامل مع النقر على زر التحميل
    async function handleDownloadClick(event) {
        console.log('تم النقر على زر med!'); // للتأكد من عمل النقر

        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        const videoId = getVideoId();
        if (!videoId) {
            alert('لا يمكن العثور على معرف الفيديو');
            return;
        }

        const medElement = document.getElementById('med-overlay');
        if (!medElement) {
            console.error('لا يمكن العثور على عنصر med');
            return;
        }

        const originalText = medElement.textContent;

        try {
            // تغيير النص لإظهار حالة التحميل
            medElement.textContent = '⬇️';
            medElement.style.backgroundColor = 'rgba(0, 100, 0, 0.8)';

            // الطريقة المبسطة: فتح صفحة تحميل خارجية
            const videoUrl = window.location.href;
            const videoTitle = getVideoTitle();

            // خيارات متعددة لخدمات التحميل الآمنة
            const downloadServices = [
                {
                    name: 'SaveFrom.net',
                    url: `https://savefrom.net/#url=${encodeURIComponent(videoUrl)}`,
                    safe: true
                },
                {
                    name: 'KeepVid',
                    url: `https://keepvid.com/?url=${encodeURIComponent(videoUrl)}`,
                    safe: true
                },
                {
                    name: 'ClipConverter',
                    url: `https://www.clipconverter.cc/2/`,
                    safe: true
                },
                {
                    name: 'Y2mate (قد يتطلب تجاوز تحذير الأمان)',
                    url: `https://www.y2mate.com/youtube/${videoId}`,
                    safe: false
                }
            ];

            // إنشاء نافذة اختيار خدمة تفاعلية
            const modal = createDownloadModal(videoTitle, downloadServices, videoUrl, videoId);
            document.body.appendChild(modal);

            // تحديث حالة الزر
            medElement.textContent = '📋';
            medElement.style.backgroundColor = 'rgba(0, 100, 200, 0.8)';

            // إعادة الزر لحالته الطبيعية بعد إغلاق النافذة
            const checkModalClosed = setInterval(() => {
                if (!document.querySelector('#med-download-modal')) {
                    medElement.textContent = originalText;
                    medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                    clearInterval(checkModalClosed);
                }
            }, 1000);

                // تحديث حالة الزر
                medElement.textContent = '✅';
                medElement.style.backgroundColor = 'rgba(0, 150, 0, 0.8)';

                // عرض رسالة نجاح
                setTimeout(() => {
                    alert('تم فتح صفحة التحميل!\n\nاتبع التعليمات في الصفحة الجديدة لتحميل الفيديو.');
                }, 500);

            } else {
                // المستخدم ألغى العملية
                medElement.textContent = '⏹️';
                medElement.style.backgroundColor = 'rgba(100, 100, 0, 0.8)';
            }

            // إعادة الزر لحالته الطبيعية بعد 3 ثوان
            setTimeout(() => {
                medElement.textContent = originalText;
                medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            }, 3000);

        } catch (error) {
            console.error('خطأ في تحميل الفيديو:', error);
            medElement.textContent = '❌';
            medElement.style.backgroundColor = 'rgba(150, 0, 0, 0.8)';
            setTimeout(() => {
                medElement.textContent = originalText;
                medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            }, 3000);
            alert('حدث خطأ أثناء محاولة فتح صفحة التحميل');
        }
    }

    // دالة لإضافة النص على الفيديو
    function addMedToVideo() {
        // البحث عن مشغل الفيديو
        const videoPlayer = document.querySelector('#movie_player');

        if (videoPlayer && !document.querySelector('#med-overlay')) {
            const medElement = createMedElement();

            // التأكد من إضافة الزر بشكل صحيح
            videoPlayer.style.position = 'relative';
            videoPlayer.appendChild(medElement);

            // التأكد من ظهور الزر
            setTimeout(() => {
                const addedElement = document.querySelector('#med-overlay');
                if (addedElement) {
                    console.log('تم إضافة زر "med" للتحميل على الفيديو بنجاح');

                    // إضافة حدث نقر إضافي للتأكد
                    addedElement.onclick = function(e) {
                        console.log('تم النقر على الزر!');
                        handleDownloadClick(e);
                    };
                } else {
                    console.error('فشل في إضافة زر med');
                }
            }, 100);
        }
    }

    // دالة لإنشاء نافذة اختيار خدمة التحميل
    function createDownloadModal(videoTitle, services, videoUrl, videoId) {
        const modal = document.createElement('div');
        modal.id = 'med-download-modal';
        modal.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0, 0, 0, 0.8) !important;
            z-index: 9999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            background: white !important;
            padding: 25px !important;
            border-radius: 12px !important;
            max-width: 500px !important;
            width: 90% !important;
            max-height: 80% !important;
            overflow-y: auto !important;
            direction: rtl !important;
            text-align: right !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5) !important;
        `;

        content.innerHTML = `
            <h2 style="color: #333 !important; margin-bottom: 15px !important; font-size: 20px !important;">
                🎬 تحميل فيديو YouTube
            </h2>
            <p style="color: #666 !important; margin-bottom: 20px !important; font-size: 14px !important; background: #f5f5f5 !important; padding: 10px !important; border-radius: 5px !important;">
                <strong>العنوان:</strong> ${videoTitle.substring(0, 60)}${videoTitle.length > 60 ? '...' : ''}
            </p>
            <p style="color: #333 !important; margin-bottom: 20px !important; font-size: 14px !important;">
                اختر خدمة التحميل المفضلة:
            </p>
            <div id="services-list">
                ${services.map((service, index) => `
                    <div class="service-option" data-index="${index}" style="
                        border: 2px solid ${service.safe ? '#4CAF50' : '#FF9800'} !important;
                        padding: 15px !important;
                        margin-bottom: 12px !important;
                        border-radius: 8px !important;
                        cursor: pointer !important;
                        transition: all 0.3s !important;
                        background: ${service.safe ? '#f8fff8' : '#fff8f0'} !important;
                    ">
                        <h3 style="color: #333 !important; margin: 0 0 8px 0 !important; font-size: 16px !important;">
                            ${service.safe ? '🔒' : '⚠️'} ${service.name}
                        </h3>
                        <p style="color: #666 !important; margin: 0 !important; font-size: 12px !important;">
                            ${service.safe ? 'خدمة آمنة وموثوقة' : 'قد تتطلب تجاوز تحذيرات الأمان'}
                        </p>
                    </div>
                `).join('')}
            </div>
            <div style="text-align: center !important; margin-top: 20px !important;">
                <button id="cancel-download" style="
                    background: #f44336 !important;
                    color: white !important;
                    border: none !important;
                    padding: 12px 24px !important;
                    border-radius: 6px !important;
                    cursor: pointer !important;
                    font-size: 14px !important;
                    transition: background 0.3s !important;
                ">❌ إلغاء</button>
            </div>
        `;

        modal.appendChild(content);

        // إضافة أحداث النقر
        setTimeout(() => {
            const serviceOptions = modal.querySelectorAll('.service-option');
            serviceOptions.forEach((option, index) => {
                option.addEventListener('click', () => {
                    const service = services[index];

                    // تمييز الخيار المحدد
                    serviceOptions.forEach(opt => {
                        opt.style.transform = 'scale(1)';
                        opt.style.boxShadow = 'none';
                    });
                    option.style.transform = 'scale(1.02)';
                    option.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';

                    // فتح الخدمة بعد تأخير قصير
                    setTimeout(() => {
                        window.open(service.url, '_blank');
                        document.body.removeChild(modal);
                    }, 500);
                });

                // تأثيرات التمرير
                option.addEventListener('mouseenter', () => {
                    option.style.transform = 'scale(1.02)';
                    option.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                });

                option.addEventListener('mouseleave', () => {
                    option.style.transform = 'scale(1)';
                    option.style.boxShadow = 'none';
                });
            });

            // زر الإلغاء
            const cancelBtn = modal.querySelector('#cancel-download');
            cancelBtn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }, 100);

        return modal;
    }

    // دالة لمراقبة التغييرات في الصفحة
    function observePageChanges() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // التحقق من وجود فيديو جديد
                    addMedToVideo();
                }
            });
        });

        // مراقبة التغييرات في الصفحة بأكملها
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // تشغيل الكود عند تحميل الصفحة
    function init() {
        // إضافة النص فوراً إذا كان الفيديو موجود
        addMedToVideo();
        
        // مراقبة التغييرات للفيديوهات الجديدة
        observePageChanges();
        
        // إعادة المحاولة كل ثانية للتأكد من إضافة النص
        setInterval(addMedToVideo, 1000);
    }

    // انتظار تحميل الصفحة بالكامل
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // التعامل مع التنقل في YouTube (SPA)
    let currentUrl = location.href;
    new MutationObserver(() => {
        if (location.href !== currentUrl) {
            currentUrl = location.href;
            setTimeout(addMedToVideo, 1000); // انتظار قصير لتحميل الفيديو الجديد
        }
    }).observe(document, { subtree: true, childList: true });

})();
