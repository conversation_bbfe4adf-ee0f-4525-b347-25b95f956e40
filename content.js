// إضافة اسم "med" على فيديوهات YouTube
(function() {
    'use strict';

    // دالة لإنشاء عنصر النص
    function createMedElement() {
        const medElement = document.createElement('div');
        medElement.id = 'med-overlay';
        medElement.textContent = 'med';
        medElement.className = 'med-text-overlay';
        return medElement;
    }

    // دالة لإضافة النص على الفيديو
    function addMedToVideo() {
        // البحث عن مشغل الفيديو
        const videoPlayer = document.querySelector('#movie_player');
        
        if (videoPlayer && !document.querySelector('#med-overlay')) {
            const medElement = createMedElement();
            videoPlayer.appendChild(medElement);
            console.log('تم إضافة نص "med" على الفيديو');
        }
    }

    // دالة لمراقبة التغييرات في الصفحة
    function observePageChanges() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // التحقق من وجود فيديو جديد
                    addMedToVideo();
                }
            });
        });

        // مراقبة التغييرات في الصفحة بأكملها
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // تشغيل الكود عند تحميل الصفحة
    function init() {
        // إضافة النص فوراً إذا كان الفيديو موجود
        addMedToVideo();
        
        // مراقبة التغييرات للفيديوهات الجديدة
        observePageChanges();
        
        // إعادة المحاولة كل ثانية للتأكد من إضافة النص
        setInterval(addMedToVideo, 1000);
    }

    // انتظار تحميل الصفحة بالكامل
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // التعامل مع التنقل في YouTube (SPA)
    let currentUrl = location.href;
    new MutationObserver(() => {
        if (location.href !== currentUrl) {
            currentUrl = location.href;
            setTimeout(addMedToVideo, 1000); // انتظار قصير لتحميل الفيديو الجديد
        }
    }).observe(document, { subtree: true, childList: true });

})();
