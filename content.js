// إضافة اسم "med" على فيديوهات YouTube مع إمكانية التحميل
(function() {
    'use strict';

    // دالة لاستخراج معرف الفيديو من الرابط
    function getVideoId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('v');
    }

    // دالة لاستخراج عنوان الفيديو
    function getVideoTitle() {
        const titleElement = document.querySelector('h1.ytd-video-primary-info-renderer yt-formatted-string') ||
                           document.querySelector('h1.title') ||
                           document.querySelector('#container h1');
        return titleElement ? titleElement.textContent.trim() : 'فيديو_يوتوب';
    }

    // دالة لتنظيف اسم الملف
    function sanitizeFileName(fileName) {
        return fileName.replace(/[<>:"/\\|?*]/g, '_').substring(0, 100);
    }

    // دالة لإنشاء عنصر النص
    function createMedElement() {
        const medElement = document.createElement('div');
        medElement.id = 'med-overlay';
        medElement.textContent = 'med';
        medElement.className = 'med-text-overlay';

        // إضافة خصائص إضافية للتأكد من إمكانية النقر
        medElement.style.cssText = `
            position: absolute !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 999999 !important;
            pointer-events: auto !important;
            cursor: pointer !important;
            display: block !important;
        `;

        medElement.title = 'انقر لتحميل الفيديو';

        // إضافة أحداث متعددة للتأكد من عمل النقر
        medElement.addEventListener('click', handleDownloadClick, true);
        medElement.addEventListener('mousedown', handleDownloadClick, true);
        medElement.addEventListener('touchstart', handleDownloadClick, true);

        // منع الأحداث الافتراضية من التداخل
        medElement.addEventListener('click', function(e) {
            e.stopPropagation();
            e.preventDefault();
        }, false);

        return medElement;
    }

    // دالة للتعامل مع النقر على زر التحميل
    async function handleDownloadClick(event) {
        console.log('تم النقر على زر med!'); // للتأكد من عمل النقر

        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        const videoId = getVideoId();
        if (!videoId) {
            alert('لا يمكن العثور على معرف الفيديو');
            return;
        }

        const medElement = document.getElementById('med-overlay');
        if (!medElement) {
            console.error('لا يمكن العثور على عنصر med');
            return;
        }

        const originalText = medElement.textContent;

        try {
            // تغيير النص لإظهار حالة التحميل
            medElement.textContent = '🔍';
            medElement.style.backgroundColor = 'rgba(0, 100, 200, 0.8)';

            const videoUrl = window.location.href;
            const videoTitle = getVideoTitle();

            // الحصول على صيغ الفيديو من برنامج Python
            medElement.textContent = '⏳';
            getVideoFormats(videoUrl, videoId, videoTitle, medElement, originalText);

            // تحديث حالة الزر
            medElement.textContent = '📋';
            medElement.style.backgroundColor = 'rgba(0, 100, 200, 0.8)';

            // إعادة الزر لحالته الطبيعية بعد إغلاق النافذة
            const checkModalClosed = setInterval(() => {
                if (!document.querySelector('#med-download-modal')) {
                    medElement.textContent = originalText;
                    medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                    clearInterval(checkModalClosed);
                }
            }, 1000);

        } catch (error) {
            console.error('خطأ في تحميل الفيديو:', error);
            medElement.textContent = '❌';
            medElement.style.backgroundColor = 'rgba(150, 0, 0, 0.8)';
            setTimeout(() => {
                medElement.textContent = originalText;
                medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            }, 3000);
            alert('حدث خطأ أثناء محاولة فتح صفحة التحميل');
        }
    }

    // دالة لإضافة النص على الفيديو
    function addMedToVideo() {
        // البحث عن مشغل الفيديو
        const videoPlayer = document.querySelector('#movie_player');

        if (videoPlayer && !document.querySelector('#med-overlay')) {
            const medElement = createMedElement();

            // التأكد من إضافة الزر بشكل صحيح
            videoPlayer.style.position = 'relative';
            videoPlayer.appendChild(medElement);

            // التأكد من ظهور الزر
            setTimeout(() => {
                const addedElement = document.querySelector('#med-overlay');
                if (addedElement) {
                    console.log('تم إضافة زر "med" للتحميل على الفيديو بنجاح');

                    // إضافة حدث نقر إضافي للتأكد
                    addedElement.onclick = function(e) {
                        console.log('تم النقر على الزر!');
                        handleDownloadClick(e);
                    };
                } else {
                    console.error('فشل في إضافة زر med');
                }
            }, 100);
        }
    }

    // دالة للحصول على صيغ الفيديو من برنامج Python
    async function getVideoFormats(videoUrl, videoId, videoTitle, medElement, originalText) {
        try {
            // إرسال طلب إلى background script للحصول على الصيغ
            chrome.runtime.sendMessage({
                action: 'getVideoFormats',
                videoUrl: videoUrl,
                videoId: videoId,
                videoTitle: videoTitle
            }, (response) => {
                if (response && response.success) {
                    // عرض نافذة الصيغ
                    showFormatsModal(response.data, videoUrl, videoId, videoTitle);
                    medElement.textContent = '📋';
                    medElement.style.backgroundColor = 'rgba(0, 150, 0, 0.8)';
                } else {
                    // فشل في الحصول على الصيغ - عرض الخيارات البديلة
                    showFallbackOptions(videoUrl, videoId, videoTitle);
                    medElement.textContent = '⚠️';
                    medElement.style.backgroundColor = 'rgba(200, 100, 0, 0.8)';
                }

                // إعادة الزر لحالته الطبيعية بعد 3 ثوان
                setTimeout(() => {
                    medElement.textContent = originalText;
                    medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                }, 3000);
            });

        } catch (error) {
            console.error('خطأ في الحصول على صيغ الفيديو:', error);
            // عرض الخيارات البديلة في حالة الخطأ
            showFallbackOptions(videoUrl, videoId, videoTitle);
            medElement.textContent = '❌';
            medElement.style.backgroundColor = 'rgba(150, 0, 0, 0.8)';

            setTimeout(() => {
                medElement.textContent = originalText;
                medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            }, 3000);
        }
    }

    // دالة لعرض نافذة الصيغ المتاحة
    function showFormatsModal(videoData, videoUrl, videoId, videoTitle) {
        const modal = createFormatsModal(videoData, videoUrl, videoId, videoTitle);
        document.body.appendChild(modal);
    }

    // دالة لعرض الخيارات البديلة في حالة فشل الحصول على الصيغ
    function showFallbackOptions(videoUrl, videoId, videoTitle) {
        const fallbackOptions = [
            {
                name: '📋 نسخ رابط الفيديو',
                description: 'نسخ رابط الفيديو للاستخدام في برامج التحميل',
                action: 'copy',
                safe: true
            },
            {
                name: '🐍 تشغيل برنامج Python',
                description: 'فتح برنامج Python المحلي للتحميل',
                action: 'python',
                safe: true
            },
            {
                name: '📱 استخدام تطبيق خارجي',
                description: 'تحميل برامج التحميل الموصى بها',
                action: 'app',
                safe: true
            }
        ];

        const modal = createDownloadModal(videoTitle, fallbackOptions, videoUrl, videoId);
        document.body.appendChild(modal);
    }

    // دالة لإنشاء نافذة عرض الصيغ
    function createFormatsModal(videoData, videoUrl, videoId, videoTitle) {
        const modal = document.createElement('div');
        modal.id = 'med-formats-modal';
        modal.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0, 0, 0, 0.8) !important;
            z-index: 9999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            background: white !important;
            padding: 25px !important;
            border-radius: 12px !important;
            max-width: 800px !important;
            width: 90% !important;
            max-height: 80% !important;
            overflow-y: auto !important;
            direction: rtl !important;
            text-align: right !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5) !important;
        `;

        // تجميع الصيغ حسب النوع
        const videoFormats = videoData.formats.filter(f => f.vcodec !== 'none' && f.acodec !== 'none');
        const audioFormats = videoData.formats.filter(f => f.vcodec === 'none' && f.acodec !== 'none');
        const videoOnlyFormats = videoData.formats.filter(f => f.vcodec !== 'none' && f.acodec === 'none');

        content.innerHTML = `
            <h2 style="color: #333 !important; margin-bottom: 15px !important; font-size: 20px !important;">
                🎬 صيغ الفيديو المتاحة
            </h2>
            <div style="background: #f5f5f5 !important; padding: 15px !important; border-radius: 8px !important; margin-bottom: 20px !important;">
                <h3 style="margin: 0 0 10px 0 !important; color: #333 !important;">📹 ${videoData.title}</h3>
                <p style="margin: 0 !important; color: #666 !important; font-size: 12px !important;">
                    المدة: ${formatDuration(videoData.duration)} | القناة: ${videoData.uploader} | المشاهدات: ${videoData.view_count?.toLocaleString() || 'غير معروف'}
                </p>
            </div>

            <div style="margin-bottom: 20px !important;">
                <h3 style="color: #333 !important; margin-bottom: 10px !important;">🎥 فيديو + صوت</h3>
                <div id="video-formats" style="max-height: 200px !important; overflow-y: auto !important;">
                    ${createFormatsList(videoFormats, 'video')}
                </div>
            </div>

            <div style="margin-bottom: 20px !important;">
                <h3 style="color: #333 !important; margin-bottom: 10px !important;">🎵 صوت فقط</h3>
                <div id="audio-formats" style="max-height: 150px !important; overflow-y: auto !important;">
                    ${createFormatsList(audioFormats, 'audio')}
                </div>
            </div>

            <div style="text-align: center !important; margin-top: 20px !important;">
                <button id="cancel-formats" style="
                    background: #f44336 !important;
                    color: white !important;
                    border: none !important;
                    padding: 12px 24px !important;
                    border-radius: 6px !important;
                    cursor: pointer !important;
                    font-size: 14px !important;
                    margin-left: 10px !important;
                ">❌ إلغاء</button>

                <button id="open-python" style="
                    background: #4CAF50 !important;
                    color: white !important;
                    border: none !important;
                    padding: 12px 24px !important;
                    border-radius: 6px !important;
                    cursor: pointer !important;
                    font-size: 14px !important;
                ">🐍 فتح برنامج Python</button>
            </div>
        `;

        modal.appendChild(content);

        // إضافة أحداث النقر
        setTimeout(() => {
            // أحداث اختيار الصيغة
            const formatButtons = modal.querySelectorAll('.format-option');
            formatButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const formatId = button.dataset.formatId;
                    const formatInfo = videoData.formats.find(f => f.format_id === formatId);
                    downloadWithPython(videoUrl, formatId, formatInfo, videoData);
                    document.body.removeChild(modal);
                });
            });

            // زر الإلغاء
            const cancelBtn = modal.querySelector('#cancel-formats');
            cancelBtn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });

            // زر فتح Python
            const pythonBtn = modal.querySelector('#open-python');
            pythonBtn.addEventListener('click', () => {
                openPythonDownloader(videoUrl, videoData);
                document.body.removeChild(modal);
            });

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }, 100);

        return modal;
    }

    // دالة لإنشاء قائمة الصيغ
    function createFormatsList(formats, type) {
        if (!formats || formats.length === 0) {
            return '<p style="color: #999 !important; text-align: center !important;">لا توجد صيغ متاحة</p>';
        }

        return formats.map(format => {
            const quality = format.resolution || format.quality || 'غير محدد';
            const size = formatFilesize(format.filesize);
            const ext = format.ext || 'غير محدد';

            return `
                <div class="format-option" data-format-id="${format.format_id}" style="
                    border: 2px solid #4CAF50 !important;
                    padding: 12px !important;
                    margin-bottom: 8px !important;
                    border-radius: 6px !important;
                    cursor: pointer !important;
                    transition: all 0.3s !important;
                    background: #f8fff8 !important;
                ">
                    <div style="display: flex !important; justify-content: space-between !important; align-items: center !important;">
                        <div>
                            <strong style="color: #333 !important;">${ext.toUpperCase()}</strong>
                            <span style="color: #666 !important; margin-right: 10px !important;">${quality}</span>
                        </div>
                        <div style="color: #888 !important; font-size: 12px !important;">
                            ${size} ${format.fps ? `• ${format.fps}fps` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // دالة لتنسيق مدة الفيديو
    function formatDuration(seconds) {
        if (!seconds) return 'غير معروف';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // دالة لتنسيق حجم الملف
    function formatFilesize(size) {
        if (!size) return 'غير معروف';

        const units = ['B', 'KB', 'MB', 'GB'];
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }

    // دالة لتحميل الفيديو باستخدام Python
    function downloadWithPython(videoUrl, formatId, formatInfo, videoData) {
        // إنشاء بيانات للتصدير
        const exportData = {
            url: videoUrl,
            selectedFormat: formatId,
            formatInfo: formatInfo,
            formats: videoData,
            timestamp: new Date().toISOString()
        };

        // تحميل البيانات كملف JSON
        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'med_video_data.json';
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // إظهار تعليمات للمستخدم
        setTimeout(() => {
            const instructions = `📥 تم تحميل ملف البيانات!

🔧 خطوات التحميل:

1️⃣ ضع ملف "med_video_data.json" في مجلد الإضافة
2️⃣ شغّل ملف "auto_downloader.bat"
3️⃣ أو شغّل: python youtube_downloader.py --auto-check

💡 أو انسخ هذا الأمر وشغّله في سطر الأوامر:
python youtube_downloader.py --url "${videoUrl}" --gui

هل تريد نسخ الأمر؟`;

            if (confirm(instructions)) {
                const command = `python youtube_downloader.py --url "${videoUrl}" --gui`;
                navigator.clipboard.writeText(command).then(() => {
                    alert('✅ تم نسخ الأمر! الصقه في سطر الأوامر (CMD/Terminal)');
                }).catch(() => {
                    prompt('انسخ هذا الأمر:', command);
                });
            }
        }, 1000);
    }

    // دالة لفتح برنامج Python مع جميع البيانات
    function openPythonDownloader(videoUrl, videoData) {
        chrome.runtime.sendMessage({
            action: 'openPythonGUI',
            videoUrl: videoUrl,
            videoData: videoData
        }, (response) => {
            if (response && response.success) {
                alert('✅ تم فتح برنامج Python للتحميل!');
            } else {
                alert('❌ فشل في فتح برنامج Python: ' + (response?.error || 'خطأ غير معروف'));
            }
        });
    }

    // دالة لإنشاء نافذة اختيار خدمة التحميل المحدثة
    function createDownloadModal(videoTitle, options, videoUrl, videoId) {
        const modal = document.createElement('div');
        modal.id = 'med-download-modal';
        modal.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0, 0, 0, 0.8) !important;
            z-index: 9999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            background: white !important;
            padding: 25px !important;
            border-radius: 12px !important;
            max-width: 500px !important;
            width: 90% !important;
            max-height: 80% !important;
            overflow-y: auto !important;
            direction: rtl !important;
            text-align: right !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5) !important;
        `;

        content.innerHTML = `
            <h2 style="color: #333 !important; margin-bottom: 15px !important; font-size: 20px !important;">
                🎬 تحميل فيديو YouTube
            </h2>
            <p style="color: #666 !important; margin-bottom: 20px !important; font-size: 14px !important; background: #f5f5f5 !important; padding: 10px !important; border-radius: 5px !important;">
                <strong>العنوان:</strong> ${videoTitle.substring(0, 60)}${videoTitle.length > 60 ? '...' : ''}
            </p>
            <p style="color: #333 !important; margin-bottom: 20px !important; font-size: 14px !important;">
                اختر خدمة التحميل المفضلة:
            </p>
            <div id="services-list">
                ${options.map((option, index) => `
                    <div class="service-option" data-index="${index}" style="
                        border: 2px solid ${option.safe ? '#4CAF50' : '#FF9800'} !important;
                        padding: 15px !important;
                        margin-bottom: 12px !important;
                        border-radius: 8px !important;
                        cursor: pointer !important;
                        transition: all 0.3s !important;
                        background: ${option.safe ? '#f8fff8' : '#fff8f0'} !important;
                    ">
                        <h3 style="color: #333 !important; margin: 0 0 8px 0 !important; font-size: 16px !important;">
                            ${option.name}
                        </h3>
                        <p style="color: #666 !important; margin: 0 !important; font-size: 12px !important;">
                            ${option.description}
                        </p>
                    </div>
                `).join('')}
            </div>
            <div style="text-align: center !important; margin-top: 20px !important;">
                <button id="cancel-download" style="
                    background: #f44336 !important;
                    color: white !important;
                    border: none !important;
                    padding: 12px 24px !important;
                    border-radius: 6px !important;
                    cursor: pointer !important;
                    font-size: 14px !important;
                    transition: background 0.3s !important;
                ">❌ إلغاء</button>
            </div>
        `;

        modal.appendChild(content);

        // إضافة أحداث النقر
        setTimeout(() => {
            const serviceOptions = modal.querySelectorAll('.service-option');
            serviceOptions.forEach((option, index) => {
                option.addEventListener('click', () => {
                    const selectedOption = options[index];

                    // تمييز الخيار المحدد
                    serviceOptions.forEach(opt => {
                        opt.style.transform = 'scale(1)';
                        opt.style.boxShadow = 'none';
                    });
                    option.style.transform = 'scale(1.02)';
                    option.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';

                    // تنفيذ الإجراء المحدد
                    setTimeout(() => {
                        executeDownloadAction(selectedOption, videoUrl, videoId, videoTitle);
                        document.body.removeChild(modal);
                    }, 500);
                });

                // تأثيرات التمرير
                option.addEventListener('mouseenter', () => {
                    option.style.transform = 'scale(1.02)';
                    option.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                });

                option.addEventListener('mouseleave', () => {
                    option.style.transform = 'scale(1)';
                    option.style.boxShadow = 'none';
                });
            });

            // زر الإلغاء
            const cancelBtn = modal.querySelector('#cancel-download');
            cancelBtn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });

            // إغلاق عند النقر خارج النافذة
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }, 100);

        return modal;
    }

    // دالة لتنفيذ إجراءات التحميل المختلفة
    function executeDownloadAction(option, videoUrl, videoId, videoTitle) {
        switch (option.action) {
            case 'copy':
                // نسخ رابط الفيديو
                navigator.clipboard.writeText(videoUrl).then(() => {
                    alert(`✅ تم نسخ رابط الفيديو!\n\n${videoUrl}\n\nيمكنك الآن استخدامه في:\n• 4K Video Downloader\n• JDownloader\n• IDM\n• أي برنامج تحميل آخر`);
                }).catch(() => {
                    prompt('انسخ هذا الرابط:', videoUrl);
                });
                break;

            case 'incognito':
                // فتح في وضع التصفح الخفي (محاكاة)
                alert(`🔗 سيتم فتح Y2mate في تبويب جديد\n\nنصائح لتجنب تحذيرات الأمان:\n• استخدم وضع التصفح الخفي\n• انقر "الإعدادات المتقدمة" ثم "الانتقال للموقع"\n• أو جرب نسخ الرابط واستخدام برنامج تحميل`);
                setTimeout(() => {
                    window.open(option.url, '_blank');
                }, 2000);
                break;

            case 'app':
                // اقتراح تطبيقات خارجية
                const appSuggestions = `📱 تطبيقات التحميل الموصى بها:\n\n` +
                    `🔹 4K Video Downloader (مجاني)\n` +
                    `🔹 JDownloader (مجاني ومفتوح المصدر)\n` +
                    `🔹 Internet Download Manager (مدفوع)\n` +
                    `🔹 YTD Video Downloader (مجاني)\n\n` +
                    `رابط الفيديو: ${videoUrl}\n\n` +
                    `هل تريد البحث عن هذه التطبيقات؟`;

                if (confirm(appSuggestions)) {
                    window.open('https://www.4kdownload.com/products/product-videodownloader', '_blank');
                }
                break;

            case 'direct':
                // فتح مباشر مع تعليمات
                const instructions = `⚠️ تعليمات تجاوز تحذيرات الأمان:\n\n` +
                    `1. عند ظهور "الاتصال غير آمن":\n` +
                    `   • انقر "الإعدادات المتقدمة"\n` +
                    `   • انقر "الانتقال إلى الموقع"\n\n` +
                    `2. أو استخدم متصفح آخر\n\n` +
                    `3. أو انسخ الرابط واستخدم برنامج تحميل\n\n` +
                    `هل تريد المتابعة؟`;

                if (confirm(instructions)) {
                    window.open(option.url, '_blank');
                }
                break;

            case 'alternatives':
                // عرض بدائل أخرى
                const alternatives = `🔧 بدائل أخرى للتحميل:\n\n` +
                    `📋 الطريقة الأسهل:\n` +
                    `1. انسخ رابط الفيديو: ${videoUrl}\n` +
                    `2. اذهب إلى موقع تحميل آمن\n` +
                    `3. الصق الرابط وحمّل\n\n` +
                    `🌐 مواقع بديلة:\n` +
                    `• ssyoutube.com (أضف ss قبل youtube في الرابط)\n` +
                    `• 9xbuddy.com\n` +
                    `• savefrom.net\n\n` +
                    `📱 تطبيقات الهاتف:\n` +
                    `• TubeMate (Android)\n` +
                    `• Documents by Readdle (iOS)\n\n` +
                    `هل تريد نسخ الرابط؟`;

                if (confirm(alternatives)) {
                    navigator.clipboard.writeText(videoUrl).then(() => {
                        alert('✅ تم نسخ الرابط!');
                    }).catch(() => {
                        prompt('انسخ هذا الرابط:', videoUrl);
                    });
                }
                break;

            default:
                alert('خيار غير مدعوم');
        }
    }

    // دالة لمراقبة التغييرات في الصفحة
    function observePageChanges() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // التحقق من وجود فيديو جديد
                    addMedToVideo();
                }
            });
        });

        // مراقبة التغييرات في الصفحة بأكملها
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // تشغيل الكود عند تحميل الصفحة
    function init() {
        // إضافة النص فوراً إذا كان الفيديو موجود
        addMedToVideo();
        
        // مراقبة التغييرات للفيديوهات الجديدة
        observePageChanges();
        
        // إعادة المحاولة كل ثانية للتأكد من إضافة النص
        setInterval(addMedToVideo, 1000);
    }

    // انتظار تحميل الصفحة بالكامل
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // التعامل مع التنقل في YouTube (SPA)
    let currentUrl = location.href;
    new MutationObserver(() => {
        if (location.href !== currentUrl) {
            currentUrl = location.href;
            setTimeout(addMedToVideo, 1000); // انتظار قصير لتحميل الفيديو الجديد
        }
    }).observe(document, { subtree: true, childList: true });

})();
