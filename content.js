// إضافة اسم "med" على فيديوهات YouTube مع إمكانية التحميل
(function() {
    'use strict';

    // دالة لاستخراج معرف الفيديو من الرابط
    function getVideoId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('v');
    }

    // دالة لاستخراج عنوان الفيديو
    function getVideoTitle() {
        const titleElement = document.querySelector('h1.ytd-video-primary-info-renderer yt-formatted-string') ||
                           document.querySelector('h1.title') ||
                           document.querySelector('#container h1');
        return titleElement ? titleElement.textContent.trim() : 'فيديو_يوتوب';
    }

    // دالة لتنظيف اسم الملف
    function sanitizeFileName(fileName) {
        return fileName.replace(/[<>:"/\\|?*]/g, '_').substring(0, 100);
    }

    // دالة لإنشاء عنصر النص
    function createMedElement() {
        const medElement = document.createElement('div');
        medElement.id = 'med-overlay';
        medElement.textContent = 'med';
        medElement.className = 'med-text-overlay';

        // إضافة خصائص إضافية للتأكد من إمكانية النقر
        medElement.style.cssText = `
            position: absolute !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 999999 !important;
            pointer-events: auto !important;
            cursor: pointer !important;
            display: block !important;
        `;

        medElement.title = 'انقر لتحميل الفيديو';

        // إضافة أحداث متعددة للتأكد من عمل النقر
        medElement.addEventListener('click', handleDownloadClick, true);
        medElement.addEventListener('mousedown', handleDownloadClick, true);
        medElement.addEventListener('touchstart', handleDownloadClick, true);

        // منع الأحداث الافتراضية من التداخل
        medElement.addEventListener('click', function(e) {
            e.stopPropagation();
            e.preventDefault();
        }, false);

        return medElement;
    }

    // دالة للتعامل مع النقر على زر التحميل
    async function handleDownloadClick(event) {
        console.log('تم النقر على زر med!'); // للتأكد من عمل النقر

        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        const videoId = getVideoId();
        if (!videoId) {
            alert('لا يمكن العثور على معرف الفيديو');
            return;
        }

        const medElement = document.getElementById('med-overlay');
        if (!medElement) {
            console.error('لا يمكن العثور على عنصر med');
            return;
        }

        const originalText = medElement.textContent;

        try {
            // تغيير النص لإظهار حالة التحميل
            medElement.textContent = '⬇️';
            medElement.style.backgroundColor = 'rgba(0, 100, 0, 0.8)';

            // إرسال رسالة إلى background script
            chrome.runtime.sendMessage({
                action: 'downloadVideo',
                videoId: videoId,
                videoTitle: getVideoTitle(),
                videoUrl: window.location.href
            }, (response) => {
                if (response && response.success) {
                    medElement.textContent = '✅';
                    medElement.style.backgroundColor = 'rgba(0, 150, 0, 0.8)';
                    setTimeout(() => {
                        medElement.textContent = originalText;
                        medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                    }, 3000);
                } else {
                    medElement.textContent = '❌';
                    medElement.style.backgroundColor = 'rgba(150, 0, 0, 0.8)';
                    setTimeout(() => {
                        medElement.textContent = originalText;
                        medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                    }, 3000);
                    alert('فشل في تحميل الفيديو: ' + (response?.error || 'خطأ غير معروف'));
                }
            });

        } catch (error) {
            console.error('خطأ في تحميل الفيديو:', error);
            medElement.textContent = '❌';
            medElement.style.backgroundColor = 'rgba(150, 0, 0, 0.8)';
            setTimeout(() => {
                medElement.textContent = originalText;
                medElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            }, 3000);
            alert('حدث خطأ أثناء محاولة تحميل الفيديو');
        }
    }

    // دالة لإضافة النص على الفيديو
    function addMedToVideo() {
        // البحث عن مشغل الفيديو
        const videoPlayer = document.querySelector('#movie_player');

        if (videoPlayer && !document.querySelector('#med-overlay')) {
            const medElement = createMedElement();

            // التأكد من إضافة الزر بشكل صحيح
            videoPlayer.style.position = 'relative';
            videoPlayer.appendChild(medElement);

            // التأكد من ظهور الزر
            setTimeout(() => {
                const addedElement = document.querySelector('#med-overlay');
                if (addedElement) {
                    console.log('تم إضافة زر "med" للتحميل على الفيديو بنجاح');

                    // إضافة حدث نقر إضافي للتأكد
                    addedElement.onclick = function(e) {
                        console.log('تم النقر على الزر!');
                        handleDownloadClick(e);
                    };
                } else {
                    console.error('فشل في إضافة زر med');
                }
            }, 100);
        }
    }

    // دالة لمراقبة التغييرات في الصفحة
    function observePageChanges() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // التحقق من وجود فيديو جديد
                    addMedToVideo();
                }
            });
        });

        // مراقبة التغييرات في الصفحة بأكملها
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // تشغيل الكود عند تحميل الصفحة
    function init() {
        // إضافة النص فوراً إذا كان الفيديو موجود
        addMedToVideo();
        
        // مراقبة التغييرات للفيديوهات الجديدة
        observePageChanges();
        
        // إعادة المحاولة كل ثانية للتأكد من إضافة النص
        setInterval(addMedToVideo, 1000);
    }

    // انتظار تحميل الصفحة بالكامل
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // التعامل مع التنقل في YouTube (SPA)
    let currentUrl = location.href;
    new MutationObserver(() => {
        if (location.href !== currentUrl) {
            currentUrl = location.href;
            setTimeout(addMedToVideo, 1000); // انتظار قصير لتحميل الفيديو الجديد
        }
    }).observe(document, { subtree: true, childList: true });

})();
