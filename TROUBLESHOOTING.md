# 🔧 استكشاف الأخطاء وحلها - Med YouTube Downloader Pro

## ❌ خطأ Service Worker Registration Failed (Status code: 15)

### 🔍 سبب المشكلة:
هذا خطأ شائع في Chrome Extensions مع Manifest V3 يحدث عادة بسبب:
- أخطاء في بناء الجملة في ملف `background.js`
- استخدام APIs غير متوافقة مع Service Workers
- ملفات كبيرة جداً أو معقدة

### ✅ الحلول:

#### 1. إعادة تحميل الإضافة:
1. اذهب إلى `chrome://extensions/`
2. انقر على أيقونة التحديث 🔄 بجانب الإضافة
3. أو احذف الإضافة وأعد تحميلها

#### 2. فحص Console للأخطاء:
1. في `chrome://extensions/`
2. انقر "فحص العروض" أو "Inspect views" → "service worker"
3. اب<PERSON><PERSON> عن أخطاء JavaScript في Console

#### 3. تبسيط ملف background.js:
تم إنشاء ملف `background.js` مبسط يحل هذه المشكلة.

---

## 🐛 مشاكل شائعة أخرى

### المشكلة: زر "med" لا يظهر
**الحلول:**
- تأكد من تفعيل الإضافة في `chrome://extensions/`
- أعد تحميل صفحة YouTube (F5)
- تحقق من أن الرابط يحتوي على `youtube.com`
- جرب فيديو آخر

### المشكلة: النقر على "med" لا يعمل
**الحلول:**
- افتح Console (F12) وابحث عن أخطاء
- تأكد من عدم وجود إضافات أخرى تتعارض
- جرب تعطيل مانع الإعلانات مؤقتاً
- أعد تشغيل المتصفح

### المشكلة: نافذة الصيغ لا تظهر
**الحلول:**
- تحقق من أن Service Worker يعمل
- في `chrome://extensions/` انقر "فحص العروض" → "service worker"
- ابحث عن رسائل في Console
- جرب إعادة تحميل الإضافة

### المشكلة: برنامج Python لا يفتح
**الحلول:**
- تأكد من تثبيت Python على النظام
- شغّل `python --version` في سطر الأوامر
- تأكد من وجود ملف `youtube_downloader.py`
- جرب تشغيل البرنامج يدوياً: `python youtube_downloader.py --gui`

---

## 🔧 خطوات التشخيص

### 1. فحص الإضافة:
```javascript
// في Console المتصفح (F12)
chrome.runtime.getManifest()
// يجب أن يعرض معلومات الإضافة
```

### 2. فحص Service Worker:
1. اذهب إلى `chrome://extensions/`
2. انقر "فحص العروض" → "service worker"
3. تحقق من وجود رسائل الخطأ

### 3. فحص Content Script:
1. افتح فيديو YouTube
2. اضغط F12 → Console
3. ابحث عن رسائل من الإضافة

### 4. فحص الصلاحيات:
```json
// في manifest.json
{
  "permissions": [
    "activeTab",
    "notifications", 
    "storage"
  ]
}
```

---

## 🚀 إعادة التثبيت الكاملة

إذا لم تنجح الحلول السابقة:

### 1. حذف الإضافة:
1. اذهب إلى `chrome://extensions/`
2. انقر "إزالة" على الإضافة

### 2. مسح البيانات:
1. اذهب إلى `chrome://settings/content/all`
2. ابحث عن `chrome-extension://`
3. احذف بيانات الإضافة

### 3. إعادة التثبيت:
1. أعد تحميل الإضافة
2. تأكد من تفعيلها
3. اختبر على فيديو YouTube

---

## 📊 فحص النظام

### تشغيل اختبار شامل:
```bash
python test_system.py
```

### فحص Python:
```bash
python --version
python -c "import yt_dlp; print('yt-dlp OK')"
python -c "import tkinter; print('GUI OK')"
```

### فحص الملفات:
تأكد من وجود هذه الملفات:
- ✅ `manifest.json`
- ✅ `background.js`
- ✅ `content.js`
- ✅ `styles.css`
- ✅ `popup.html`
- ✅ `popup.js`
- ✅ `youtube_downloader.py`

---

## 🆘 طلب المساعدة

إذا استمرت المشاكل:

### معلومات مطلوبة للدعم:
1. **إصدار Chrome:** `chrome://version/`
2. **نظام التشغيل:** Windows/Mac/Linux
3. **رسائل الخطأ:** من Console
4. **خطوات إعادة الإنتاج:** ما فعلته بالضبط

### أدوات التشخيص:
```bash
# معلومات النظام
python test_system.py

# فحص الإضافة
# في Console المتصفح
console.log(chrome.runtime.getManifest())
```

---

## 💡 نصائح الوقاية

### لتجنب المشاكل مستقبلاً:
1. **أبق Chrome محدثاً**
2. **لا تعدل ملفات الإضافة يدوياً**
3. **استخدم أحدث إصدار من Python**
4. **تحقق من التحديثات دورياً**

### إعدادات Chrome الموصى بها:
- تفعيل JavaScript
- السماح بالإشعارات للإضافة
- عدم حظر النوافذ المنبثقة لـ YouTube

---

## ✅ التحقق من نجاح الإصلاح

بعد تطبيق الحلول:

1. **اذهب إلى YouTube**
2. **افتح أي فيديو**
3. **ابحث عن زر "med"** في الزاوية اليمنى العلوية
4. **انقر على الزر** - يجب أن تظهر نافذة الصيغ
5. **اختر صيغة** - يجب أن يظهر إشعار Python

إذا نجحت جميع الخطوات، فقد تم حل المشكلة! 🎉
