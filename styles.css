/* تنسيق نص "med" على فيديوهات YouTube */
.med-text-overlay {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #ffffff;
    font-family: 'Arial', sans-serif;
    font-size: 24px;
    font-weight: bold;
    padding: 10px 15px;
    border-radius: 8px;
    z-index: 9999;
    pointer-events: none;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    border: 2px solid #ff0000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: fadeIn 0.5s ease-in-out;
}

/* تأثير الظهور التدريجي */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تأثير عند التمرير فوق النص */
.med-text-overlay:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease-in-out;
}

/* تنسيق مخصص للشاشات الصغيرة */
@media (max-width: 768px) {
    .med-text-overlay {
        font-size: 18px;
        padding: 8px 12px;
        top: 10px;
        right: 10px;
    }
}
