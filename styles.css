/* تنسيق زر "med" للتحميل على فيديوهات YouTube */
.med-text-overlay {
    position: absolute !important;
    top: 20px !important;
    right: 20px !important;
    background-color: rgba(0, 0, 0, 0.9) !important;
    color: #ffffff !important;
    font-family: 'Arial', sans-serif !important;
    font-size: 24px !important;
    font-weight: bold !important;
    padding: 12px 18px !important;
    border-radius: 8px !important;
    z-index: 999999 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
    border: 3px solid #ff0000 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
    animation: fadeIn 0.5s ease-in-out !important;
    transition: all 0.3s ease !important;
    user-select: none !important;
    display: block !important;
    width: auto !important;
    height: auto !important;
    min-width: 60px !important;
    min-height: 40px !important;
    text-align: center !important;
    line-height: 1 !important;
}

/* تأثير الظهور التدريجي */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تأثير عند التمرير فوق الزر */
.med-text-overlay:hover {
    transform: scale(1.1) !important;
    background-color: rgba(255, 0, 0, 0.9) !important;
    border-color: #ffffff !important;
    box-shadow: 0 6px 16px rgba(255, 0, 0, 0.6) !important;
    cursor: pointer !important;
}

/* تأثير عند النقر */
.med-text-overlay:active {
    transform: scale(0.95) !important;
    background-color: rgba(200, 0, 0, 0.9) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.8) !important;
}

/* تأثير إضافي للتأكد من إمكانية النقر */
.med-text-overlay:focus {
    outline: 2px solid #ffffff !important;
    background-color: rgba(255, 0, 0, 0.8) !important;
}

/* تنسيق مخصص للشاشات الصغيرة */
@media (max-width: 768px) {
    .med-text-overlay {
        font-size: 18px;
        padding: 8px 12px;
        top: 10px;
        right: 10px;
    }
}
