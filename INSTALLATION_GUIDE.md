# 🚀 دليل التثبيت والإعداد - Med YouTube Downloader Pro

## 📋 المتطلبات

### 1. متطلبات النظام:
- **Windows 10/11** أو **macOS 10.14+** أو **Linux Ubuntu 18.04+**
- **Google Chrome** أو **Microsoft Edge** (أحدث إصدار)
- **Python 3.7+** مثبت على النظام
- **اتصال إنترنت** مستقر

### 2. مساحة التخزين:
- **50 MB** لملفات الإضافة والبرنامج
- **مساحة إضافية** حسب حجم الفيديوهات المحملة

---

## 🔧 خطوات التثبيت

### الخطوة 1: تثبيت Python

#### Windows:
1. اذهب إلى https://python.org/downloads/
2. حمّل أحدث إصدار Python 3.x
3. **مهم:** تأكد من تفعيل "Add Python to PATH" أثناء التثبيت
4. اختبر التثبيت: افتح CMD واكتب `python --version`

#### macOS:
```bash
# باستخدام Homebrew (موصى به)
brew install python3

# أو حمّل من الموقع الرسمي
# https://python.org/downloads/macos/
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install python3 python3-pip python3-tk
```

### الخطوة 2: تحميل الإضافة
1. حمّل جميع ملفات الإضافة في مجلد واحد
2. تأكد من وجود هذه الملفات:
   ```
   📁 med-youtube-downloader/
   ├── 📄 manifest.json
   ├── 📄 content.js
   ├── 📄 background.js
   ├── 📄 styles.css
   ├── 📄 popup.html
   ├── 📄 popup.js
   ├── 🐍 youtube_downloader.py
   ├── 🐍 setup.py
   ├── 📄 requirements.txt
   └── 📖 README.md
   ```

### الخطوة 3: تثبيت المكتبات المطلوبة
1. **افتح سطر الأوامر** في مجلد الإضافة:
   - Windows: `Shift + Right Click` → "Open PowerShell here"
   - macOS/Linux: افتح Terminal وانتقل للمجلد

2. **شغّل سكريپت الإعداد:**
   ```bash
   python setup.py
   ```

3. **أو ثبّت المكتبات يدوياً:**
   ```bash
   pip install yt-dlp requests
   ```

### الخطوة 4: تثبيت الإضافة في Chrome
1. افتح Chrome واذهب إلى `chrome://extensions/`
2. فعّل **"وضع المطور"** (Developer mode)
3. انقر **"تحميل غير مضغوط"** (Load unpacked)
4. اختر مجلد الإضافة
5. تأكد من ظهور الإضافة وتفعيلها

---

## ✅ اختبار التثبيت

### 1. اختبار Python:
```bash
python youtube_downloader.py --help
```
يجب أن تظهر رسالة المساعدة.

### 2. اختبار الإضافة:
1. اذهب إلى أي فيديو YouTube
2. ابحث عن زر **"med"** في الزاوية اليمنى العلوية
3. انقر على الزر - يجب أن تظهر نافذة الصيغ

### 3. اختبار التكامل:
1. انقر على زر "med"
2. اختر أي صيغة من القائمة
3. يجب أن يفتح برنامج Python تلقائياً

---

## 🎯 الاستخدام السريع

### الطريقة 1: من الإضافة (الأسهل)
1. **افتح فيديو YouTube**
2. **انقر زر "med"** 
3. **اختر الصيغة المطلوبة**
4. **سيفتح برنامج Python** مع جميع الخيارات
5. **اختر مجلد التحميل** وانقر "تحميل"

### الطريقة 2: من سطر الأوامر
```bash
# عرض الصيغ المتاحة
python youtube_downloader.py --url "https://youtube.com/watch?v=VIDEO_ID" --get-formats

# فتح الواجهة الرسومية
python youtube_downloader.py --gui
```

### الطريقة 3: من ملفات التشغيل
- **Windows:** انقر مرتين على `run_downloader.bat`
- **Linux/Mac:** شغّل `./run_downloader.sh --gui`

---

## 🔧 استكشاف الأخطاء

### مشكلة: "Python غير موجود"
**الحل:**
```bash
# Windows
where python
# إذا لم يظهر شيء، أعد تثبيت Python مع تفعيل PATH

# macOS/Linux  
which python3
# استخدم python3 بدلاً من python
```

### مشكلة: "yt-dlp غير مثبت"
**الحل:**
```bash
pip install --upgrade yt-dlp
# أو
pip3 install --upgrade yt-dlp
```

### مشكلة: "الإضافة لا تعمل"
**الحل:**
1. تأكد من تفعيل الإضافة في `chrome://extensions/`
2. أعد تحميل صفحة YouTube
3. تحقق من Console للأخطاء (F12)

### مشكلة: "برنامج Python لا يفتح"
**الحل:**
1. تأكد من وجود ملف `youtube_downloader.py` في نفس مجلد الإضافة
2. جرب تشغيل البرنامج يدوياً: `python youtube_downloader.py --gui`
3. تحقق من صلاحيات الملفات

---

## ⚙️ الإعدادات المتقدمة

### تخصيص مجلد التحميل:
1. انقر على أيقونة الإضافة
2. اختر "إعدادات متقدمة"
3. حدد مجلد التحميل الافتراضي

### تخصيص جودة التحميل:
```python
# في ملف youtube_downloader.py، غيّر:
ydl_opts = {
    'format': 'best[height<=720]',  # أقصى جودة 720p
    # أو
    'format': 'worst',              # أقل جودة (لتوفير البيانات)
}
```

---

## 📱 استخدام على أنظمة مختلفة

### Windows:
- استخدم PowerShell أو CMD
- يمكن إنشاء اختصار سطح المكتب لـ `run_downloader.bat`

### macOS:
- استخدم Terminal
- قد تحتاج لتثبيت Xcode Command Line Tools: `xcode-select --install`

### Linux:
- تأكد من تثبيت `python3-tk` للواجهة الرسومية
- قد تحتاج لتثبيت `ffmpeg` لبعض التحويلات

---

## 🔒 الأمان والخصوصية

### البيانات المحفوظة:
- **لا يتم حفظ** روابط الفيديوهات
- **لا يتم إرسال** بيانات لخوادم خارجية
- **جميع العمليات محلية** على جهازك

### الصلاحيات المطلوبة:
- **activeTab:** للوصول لصفحة YouTube الحالية
- **notifications:** لعرض إشعارات التحميل
- **storage:** لحفظ الإعدادات والإحصائيات

---

## 📞 الحصول على المساعدة

### إذا واجهت مشاكل:
1. **تحقق من متطلبات النظام**
2. **أعد تشغيل المتصفح**
3. **جرب إعادة تثبيت Python**
4. **تأكد من اتصال الإنترنت**

### للدعم التقني:
- تحقق من ملف `TROUBLESHOOTING.md`
- راجع الأسئلة الشائعة في `FAQ.md`
- ابحث عن حلول في مجتمع Python

---

## 🎉 تهانينا!

إذا وصلت هنا، فقد تم تثبيت **Med YouTube Downloader Pro** بنجاح! 

استمتع بتحميل فيديوهاتك المفضلة بجودة عالية وصيغ متعددة! 🚀
