#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Med YouTube Downloader - Setup Script
سكريپت إعداد وتثبيت المتطلبات
"""

import sys
import subprocess
import os
import platform
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    requirements = [
        "yt-dlp>=2023.12.30",
        "requests>=2.31.0"
    ]
    
    for requirement in requirements:
        try:
            print(f"تثبيت {requirement}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", requirement
            ])
            print(f"✅ تم تثبيت {requirement}")
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل تثبيت {requirement}: {e}")
            return False
    
    return True

def test_installation():
    """اختبار التثبيت"""
    print("🧪 اختبار التثبيت...")
    
    try:
        import yt_dlp
        print("✅ yt-dlp متوفر")
        
        import requests
        print("✅ requests متوفر")
        
        import tkinter
        print("✅ tkinter متوفر")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def create_batch_file():
    """إنشاء ملف batch لتشغيل البرنامج على Windows"""
    if platform.system() == "Windows":
        batch_content = f"""@echo off
cd /d "{os.getcwd()}"
python youtube_downloader.py %*
pause
"""
        
        with open("run_downloader.bat", "w", encoding="utf-8") as f:
            f.write(batch_content)
        
        print("✅ تم إنشاء run_downloader.bat")

def create_shell_script():
    """إنشاء ملف shell script لتشغيل البرنامج على Linux/Mac"""
    if platform.system() in ["Linux", "Darwin"]:
        script_content = f"""#!/bin/bash
cd "{os.getcwd()}"
python3 youtube_downloader.py "$@"
"""
        
        with open("run_downloader.sh", "w") as f:
            f.write(script_content)
        
        os.chmod("run_downloader.sh", 0o755)
        print("✅ تم إنشاء run_downloader.sh")

def create_desktop_shortcut():
    """إنشاء اختصار سطح المكتب"""
    try:
        desktop = Path.home() / "Desktop"
        if not desktop.exists():
            desktop = Path.home() / "سطح المكتب"
        
        if desktop.exists():
            if platform.system() == "Windows":
                # إنشاء اختصار Windows
                shortcut_path = desktop / "Med YouTube Downloader.lnk"
                # هنا يمكن استخدام مكتبة win32com لإنشاء اختصار حقيقي
                print("💡 يمكنك إنشاء اختصار يدوياً لملف run_downloader.bat")
            
            elif platform.system() == "Linux":
                # إنشاء ملف .desktop
                desktop_content = f"""[Desktop Entry]
Name=Med YouTube Downloader
Comment=تحميل فيديوهات YouTube
Exec=python3 "{os.getcwd()}/youtube_downloader.py" --gui
Icon={os.getcwd()}/icon.png
Terminal=false
Type=Application
Categories=AudioVideo;
"""
                
                desktop_file = desktop / "med-youtube-downloader.desktop"
                with open(desktop_file, "w") as f:
                    f.write(desktop_content)
                
                os.chmod(desktop_file, 0o755)
                print("✅ تم إنشاء اختصار سطح المكتب")
    
    except Exception as e:
        print(f"⚠️ لم يتم إنشاء اختصار سطح المكتب: {e}")

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    print("\n" + "="*50)
    print("🎉 تم الإعداد بنجاح!")
    print("="*50)
    print("\n📖 طرق الاستخدام:")
    print("\n1️⃣ من سطر الأوامر:")
    print("   python youtube_downloader.py --url URL --get-formats")
    print("   python youtube_downloader.py --gui")
    
    print("\n2️⃣ من الإضافة:")
    print("   • انقر على زر 'med' في فيديو YouTube")
    print("   • اختر الصيغة المطلوبة")
    print("   • سيتم فتح برنامج Python تلقائياً")
    
    if platform.system() == "Windows":
        print("\n3️⃣ من ملف batch:")
        print("   • انقر مرتين على run_downloader.bat")
    
    elif platform.system() in ["Linux", "Darwin"]:
        print("\n3️⃣ من ملف shell:")
        print("   • ./run_downloader.sh --gui")
    
    print("\n🔧 استكشاف الأخطاء:")
    print("   • تأكد من اتصال الإنترنت")
    print("   • تحقق من صحة رابط الفيديو")
    print("   • جرب تشغيل البرنامج كمدير")
    
    print("\n📁 مجلد التحميل الافتراضي:")
    print(f"   {Path.home() / 'Downloads'}")
    
    print("\n" + "="*50)

def main():
    """الدالة الرئيسية"""
    print("🚀 Med YouTube Downloader - إعداد البرنامج")
    print("="*50)
    
    # التحقق من Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار التثبيت
    if not test_installation():
        print("❌ فشل في اختبار التثبيت")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء ملفات التشغيل
    create_batch_file()
    create_shell_script()
    create_desktop_shortcut()
    
    # عرض تعليمات الاستخدام
    show_usage_instructions()
    
    # اختبار سريع
    print("\n🧪 هل تريد اختبار البرنامج الآن؟ (y/n): ", end="")
    try:
        choice = input().lower()
        if choice in ['y', 'yes', 'نعم', 'ن']:
            print("تشغيل الواجهة الرسومية...")
            subprocess.Popen([sys.executable, "youtube_downloader.py", "--gui"])
    except KeyboardInterrupt:
        print("\nتم الإلغاء")
    
    print("\n✅ انتهى الإعداد!")
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
