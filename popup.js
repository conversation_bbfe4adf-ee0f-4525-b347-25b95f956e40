// Med YouTube Downloader Pro - Popup Script
document.addEventListener('DOMContentLoaded', function() {
    initializePopup();
    loadStats();
    checkPythonStatus();
    setupEventListeners();
});

function initializePopup() {
    console.log('Med YouTube Downloader Pro - Popup loaded');
}

function loadStats() {
    // تحميل الإحصائيات من التخزين المحلي
    chrome.storage.local.get(['downloadsCount', 'formatsChecked'], function(result) {
        document.getElementById('downloads-count').textContent = result.downloadsCount || 0;
        document.getElementById('formats-checked').textContent = result.formatsChecked || 0;
    });
}

function checkPythonStatus() {
    // فحص حالة Python
    chrome.runtime.sendMessage({
        action: 'checkPythonStatus'
    }, function(response) {
        const pythonStatus = document.getElementById('python-status');
        const ytdlpStatus = document.getElementById('ytdlp-status');
        
        if (response && response.pythonAvailable) {
            pythonStatus.className = 'status-indicator online';
        } else {
            pythonStatus.className = 'status-indicator offline';
        }
        
        if (response && response.ytdlpAvailable) {
            ytdlpStatus.className = 'status-indicator online';
        } else {
            ytdlpStatus.className = 'status-indicator offline';
        }
    });
}

function setupEventListeners() {
    // زر اختبار Python
    document.getElementById('test-python').addEventListener('click', function() {
        this.textContent = '🔄 جاري الاختبار...';
        this.disabled = true;
        
        chrome.runtime.sendMessage({
            action: 'testPython'
        }, (response) => {
            if (response && response.success) {
                this.textContent = '✅ Python يعمل بشكل صحيح';
                this.style.background = '#4CAF50';
            } else {
                this.textContent = '❌ Python غير متوفر';
                this.style.background = '#f44336';
            }
            
            setTimeout(() => {
                this.textContent = '🧪 اختبار Python';
                this.style.background = '';
                this.disabled = false;
            }, 3000);
        });
    });

    // زر فتح برنامج التحميل
    document.getElementById('open-downloader').addEventListener('click', function() {
        chrome.runtime.sendMessage({
            action: 'openPythonGUI',
            videoUrl: '',
            videoData: {}
        }, (response) => {
            if (response && response.success) {
                showNotification('تم فتح برنامج التحميل', 'success');
            } else {
                showNotification('فشل في فتح برنامج التحميل', 'error');
            }
        });
    });

    // زر تثبيت المتطلبات
    document.getElementById('install-python').addEventListener('click', function() {
        const instructions = `📦 تعليمات تثبيت المتطلبات:

1️⃣ تأكد من تثبيت Python 3.7+
2️⃣ افتح سطر الأوامر (CMD/Terminal)
3️⃣ انتقل إلى مجلد الإضافة
4️⃣ شغّل: python setup.py

أو قم بتثبيت المكتبات يدوياً:
pip install yt-dlp requests

هل تريد فتح دليل التثبيت المفصل؟`;

        if (confirm(instructions)) {
            chrome.tabs.create({
                url: 'https://github.com/yt-dlp/yt-dlp#installation'
            });
        }
    });

    // زر دليل الاستخدام
    document.getElementById('open-guide').addEventListener('click', function() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('README.md')
        });
    });

    // زر الإعدادات
    document.getElementById('settings').addEventListener('click', function() {
        showSettingsModal();
    });

    // زر إعادة التعيين
    document.getElementById('reset').addEventListener('click', function() {
        if (confirm('هل تريد إعادة تعيين جميع الإعدادات والإحصائيات؟')) {
            chrome.storage.local.clear(function() {
                loadStats();
                showNotification('تم إعادة تعيين الإعدادات', 'success');
            });
        }
    });
}

function showNotification(message, type) {
    // إنشاء إشعار مؤقت
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        padding: 10px 15px;
        border-radius: 5px;
        color: white;
        font-size: 12px;
        z-index: 1000;
        animation: slideIn 0.3s ease;
        background: ${type === 'success' ? '#4CAF50' : '#f44336'};
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function showSettingsModal() {
    // إنشاء نافذة إعدادات بسيطة
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;
    
    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 10px;
        max-width: 300px;
        width: 90%;
        color: #333;
        text-align: right;
        direction: rtl;
    `;
    
    content.innerHTML = `
        <h3 style="margin-top: 0;">⚙️ الإعدادات المتقدمة</h3>
        
        <div style="margin-bottom: 15px;">
            <label>
                <input type="checkbox" id="auto-detect-quality"> 
                اكتشاف الجودة تلقائياً
            </label>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>
                <input type="checkbox" id="show-notifications"> 
                عرض الإشعارات
            </label>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>مجلد التحميل الافتراضي:</label>
            <input type="text" id="default-path" style="width: 100%; margin-top: 5px; padding: 5px;">
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button id="save-settings" style="
                background: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                cursor: pointer;
                margin-left: 10px;
            ">حفظ</button>
            
            <button id="cancel-settings" style="
                background: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                cursor: pointer;
            ">إلغاء</button>
        </div>
    `;
    
    modal.appendChild(content);
    document.body.appendChild(modal);
    
    // تحميل الإعدادات الحالية
    chrome.storage.local.get(['autoDetectQuality', 'showNotifications', 'defaultPath'], function(result) {
        document.getElementById('auto-detect-quality').checked = result.autoDetectQuality || false;
        document.getElementById('show-notifications').checked = result.showNotifications !== false;
        document.getElementById('default-path').value = result.defaultPath || '';
    });
    
    // أحداث الأزرار
    document.getElementById('save-settings').addEventListener('click', function() {
        const settings = {
            autoDetectQuality: document.getElementById('auto-detect-quality').checked,
            showNotifications: document.getElementById('show-notifications').checked,
            defaultPath: document.getElementById('default-path').value
        };
        
        chrome.storage.local.set(settings, function() {
            modal.remove();
            showNotification('تم حفظ الإعدادات', 'success');
        });
    });
    
    document.getElementById('cancel-settings').addEventListener('click', function() {
        modal.remove();
    });
    
    // إغلاق عند النقر خارج النافذة
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// تحديث الإحصائيات عند استقبال رسائل
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'updateStats') {
        loadStats();
    }
});
