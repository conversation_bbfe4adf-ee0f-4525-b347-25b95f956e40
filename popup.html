<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Med YouTube Downloader Pro</title>
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            direction: rtl;
            text-align: right;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
        }

        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.8;
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }

        .section h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #fff;
        }

        .status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-indicator.online {
            background: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }

        .status-indicator.offline {
            background: #f44336;
            box-shadow: 0 0 10px #f44336;
        }

        .button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 8px;
            backdrop-filter: blur(5px);
        }

        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .button:active {
            transform: translateY(0);
        }

        .button.primary {
            background: #4CAF50;
        }

        .button.primary:hover {
            background: #45a049;
        }

        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .stat-number {
            font-size: 18px;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 11px;
            opacity: 0.7;
        }

        .version {
            background: rgba(255, 255, 255, 0.1);
            padding: 5px 10px;
            border-radius: 15px;
            display: inline-block;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 Med YouTube Downloader Pro</h1>
        <p>تحميل متقدم مع دعم Python</p>
    </div>

    <div class="section">
        <h3>📊 حالة النظام</h3>
        <div class="status">
            <span>إضافة المتصفح</span>
            <div class="status-indicator online"></div>
        </div>
        <div class="status">
            <span>برنامج Python</span>
            <div class="status-indicator" id="python-status"></div>
        </div>
        <div class="status">
            <span>مكتبة yt-dlp</span>
            <div class="status-indicator" id="ytdlp-status"></div>
        </div>
    </div>

    <div class="section">
        <h3>🚀 إجراءات سريعة</h3>
        <button class="button primary" id="test-python">🧪 اختبار Python</button>
        <button class="button" id="open-downloader">🐍 فتح برنامج التحميل</button>
        <button class="button" id="install-python">📦 تثبيت المتطلبات</button>
        <button class="button" id="open-guide">📖 دليل الاستخدام</button>
    </div>

    <div class="section">
        <h3>📈 الإحصائيات</h3>
        <div class="stats">
            <div class="stat-item">
                <span class="stat-number" id="downloads-count">0</span>
                <span class="stat-label">تحميلات</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="formats-checked">0</span>
                <span class="stat-label">صيغ مفحوصة</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h3>⚙️ الإعدادات</h3>
        <button class="button" id="settings">🔧 إعدادات متقدمة</button>
        <button class="button" id="reset">🔄 إعادة تعيين</button>
    </div>

    <div class="footer">
        <div>Med YouTube Downloader Pro</div>
        <div class="version">الإصدار 2.0</div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
