# إضافة Med لـ YouTube

إضافة بسيطة لمتصفح Google Chrome تظهر اسم "med" في الزاوية اليمنى العلوية من أي فيديو YouTube تتصفحه.

## المميزات

- ✅ يظهر اسم "med" على جميع فيديوهات YouTube
- ✅ تصميم جذاب مع خلفية شفافة وحدود حمراء
- ✅ يعمل مع التنقل السريع في YouTube
- ✅ متجاوب مع الشاشات المختلفة
- ✅ تأثيرات بصرية جميلة

## كيفية التثبيت

### الطريقة 1: تثبيت محلي (للتطوير)

1. **تحميل الملفات:**
   - تأكد من وجود جميع الملفات في مجلد واحد:
     - `manifest.json`
     - `content.js`
     - `styles.css`

2. **فتح إعدادات الإضافات في Chrome:**
   - اذهب إلى `chrome://extensions/`
   - أو من القائمة: المزيد من الأدوات > الإضافات

3. **تفعيل وضع المطور:**
   - قم بتفعيل "وضع المطور" (Developer mode) في الزاوية اليمنى العلوية

4. **تحميل الإضافة:**
   - انقر على "تحميل غير مضغوط" (Load unpacked)
   - اختر المجلد الذي يحتوي على ملفات الإضافة
   - انقر "تحديد مجلد"

5. **التأكد من التثبيت:**
   - ستظهر الإضافة في قائمة الإضافات المثبتة
   - تأكد من أنها مفعلة

## كيفية الاستخدام

1. اذهب إلى موقع YouTube: `https://www.youtube.com`
2. افتح أي فيديو
3. ستلاحظ ظهور كلمة "med" في الزاوية اليمنى العلوية من مشغل الفيديو

## التخصيص

يمكنك تخصيص الإضافة عبر تعديل الملفات التالية:

### تغيير النص المعروض
في ملف `content.js`، غير السطر:
```javascript
medElement.textContent = 'med';
```

### تغيير التصميم
في ملف `styles.css`، يمكنك تعديل:
- الألوان
- حجم الخط
- الموقع
- التأثيرات البصرية

### تغيير الموقع
في ملف `styles.css`، غير قيم:
```css
top: 20px;    /* المسافة من الأعلى */
right: 20px;  /* المسافة من اليمين */
```

## استكشاف الأخطاء

### الإضافة لا تعمل:
1. تأكد من تفعيل الإضافة في `chrome://extensions/`
2. أعد تحميل صفحة YouTube
3. تحقق من وحدة التحكم للأخطاء (F12)

### النص لا يظهر:
1. انتظر قليلاً حتى يتم تحميل الفيديو بالكامل
2. جرب إعادة تحميل الصفحة
3. تأكد من أن الفيديو يعمل بشكل طبيعي

## الملفات المطلوبة

- `manifest.json` - ملف التكوين الأساسي
- `content.js` - السكريبت الرئيسي
- `styles.css` - ملف التنسيق
- `README.md` - هذا الملف

## الدعم

إذا واجهت أي مشاكل، تأكد من:
- استخدام أحدث إصدار من Chrome
- تفعيل JavaScript في المتصفح
- عدم وجود إضافات أخرى تتعارض مع هذه الإضافة
