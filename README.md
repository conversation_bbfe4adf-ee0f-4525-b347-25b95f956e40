# إضافة Med YouTube Downloader

إضافة متقدمة لمتصفح Google Chrome تظهر زر "med" قابل للنقر في الزاوية اليمنى العلوية من أي فيديو YouTube، مع إمكانية تحميل الفيديو عند النقر عليه.

## المميزات

- ✅ يظهر زر "med" قابل للنقر على جميع فيديوهات YouTube
- ✅ **تحميل الفيديوهات** عند النقر على الزر
- ✅ **اختيار مجلد التحميل** يدوياً
- ✅ تصميم جذاب مع تأثيرات بصرية تفاعلية
- ✅ يعمل مع التنقل السريع في YouTube
- ✅ متجاوب مع الشاشات المختلفة
- ✅ إشعارات حالة التحميل (تحميل، نجح، فشل)

## كيفية التثبيت

### الطريقة 1: تثبيت محلي (للتطوير)

1. **تحميل الملفات:**
   - تأكد من وجود جميع الملفات في مجلد واحد:
     - `manifest.json`
     - `content.js`
     - `styles.css`

2. **فتح إعدادات الإضافات في Chrome:**
   - اذهب إلى `chrome://extensions/`
   - أو من القائمة: المزيد من الأدوات > الإضافات

3. **تفعيل وضع المطور:**
   - قم بتفعيل "وضع المطور" (Developer mode) في الزاوية اليمنى العلوية

4. **تحميل الإضافة:**
   - انقر على "تحميل غير مضغوط" (Load unpacked)
   - اختر المجلد الذي يحتوي على ملفات الإضافة
   - انقر "تحديد مجلد"

5. **التأكد من التثبيت:**
   - ستظهر الإضافة في قائمة الإضافات المثبتة
   - تأكد من أنها مفعلة

## كيفية الاستخدام

1. اذهب إلى موقع YouTube: `https://www.youtube.com`
2. افتح أي فيديو
3. ستلاحظ ظهور زر "med" في الزاوية اليمنى العلوية من مشغل الفيديو
4. **انقر على زر "med"** لفتح نافذة خيارات التحميل
5. **اختر الطريقة المفضلة:**
   - 📋 **نسخ رابط الفيديو** (الأسهل والأأمن)
   - 🔗 **فتح في تبويب خفي**
   - 📱 **استخدام تطبيق خارجي**
   - 🌐 **Y2mate مع تعليمات**
   - 🔧 **عرض بدائل أخرى**

## حالات الزر:
- **med** - الحالة العادية (جاهز للنقر)
- **⬇️** - جاري فتح صفحة التحميل
- **✅** - تم فتح صفحة التحميل بنجاح
- **⏹️** - تم إلغاء العملية
- **❌** - حدث خطأ

## 🚀 خيارات التحميل المتاحة:

### 📋 نسخ رابط الفيديو (الأفضل)
- ✅ **آمن 100%** - لا توجد تحذيرات أمان
- ✅ **يعمل مع جميع البرامج** - 4K Video Downloader, IDM, JDownloader
- ✅ **سريع ومباشر** - نسخة واحدة واستخدام فوري

### 📱 برامج التحميل الموصى بها:
- **4K Video Downloader** - مجاني وسهل ✅
- **JDownloader** - مفتوح المصدر ✅
- **Internet Download Manager (IDM)** - الأسرع ✅

### 🌐 مواقع بديلة:
- **ssyoutube.com** - أضف "ss" قبل "youtube" في الرابط
- **9xbuddy.com** - سهل ومباشر
- **savefrom.net** - موثوق ومجرب

## التخصيص

يمكنك تخصيص الإضافة عبر تعديل الملفات التالية:

### تغيير النص المعروض
في ملف `content.js`، غير السطر:
```javascript
medElement.textContent = 'med';
```

### تغيير التصميم
في ملف `styles.css`، يمكنك تعديل:
- الألوان
- حجم الخط
- الموقع
- التأثيرات البصرية

### تغيير الموقع
في ملف `styles.css`، غير قيم:
```css
top: 20px;    /* المسافة من الأعلى */
right: 20px;  /* المسافة من اليمين */
```

## استكشاف الأخطاء

### الإضافة لا تعمل:
1. تأكد من تفعيل الإضافة في `chrome://extensions/`
2. أعد تحميل صفحة YouTube
3. تحقق من وحدة التحكم للأخطاء (F12)

### النص لا يظهر:
1. انتظر قليلاً حتى يتم تحميل الفيديو بالكامل
2. جرب إعادة تحميل الصفحة
3. تأكد من أن الفيديو يعمل بشكل طبيعي

### تحذيرات الأمان في مواقع التحميل:
1. **استخدم الخدمات الآمنة أولاً** (SaveFrom.net, KeepVid)
2. **إذا ظهر تحذير أمان:**
   - انقر "الإعدادات المتقدمة" أو "Paramètres avancés"
   - انقر "الانتقال إلى الموقع" أو "Revenir en lieu sûr"
   - أو جرب خدمة أخرى من القائمة
3. **تأكد من تشغيل مضاد الفيروسات**
4. **لا تحمل ملفات مشبوهة**

## الملفات المطلوبة

- `manifest.json` - ملف التكوين الأساسي
- `content.js` - السكريبت الرئيسي
- `styles.css` - ملف التنسيق
- `README.md` - هذا الملف

## الدعم

إذا واجهت أي مشاكل، تأكد من:
- استخدام أحدث إصدار من Chrome
- تفعيل JavaScript في المتصفح
- عدم وجود إضافات أخرى تتعارض مع هذه الإضافة
