// ملف اختبار للتأكد من عمل النقر على زر med
// يمكن تشغيل هذا الكود في console المتصفح للاختبار

function testMedButton() {
    console.log('🔍 بدء اختبار زر med...');
    
    // البحث عن الزر
    const medButton = document.querySelector('#med-overlay');
    
    if (!medButton) {
        console.error('❌ لم يتم العثور على زر med');
        return false;
    }
    
    console.log('✅ تم العثور على زر med');
    
    // اختبار الخصائص
    const computedStyle = window.getComputedStyle(medButton);
    
    console.log('📊 خصائص الزر:');
    console.log('- الموقع:', computedStyle.position);
    console.log('- z-index:', computedStyle.zIndex);
    console.log('- pointer-events:', computedStyle.pointerEvents);
    console.log('- cursor:', computedStyle.cursor);
    console.log('- display:', computedStyle.display);
    
    // اختبار إمكانية النقر
    const rect = medButton.getBoundingClientRect();
    console.log('📐 موقع الزر:', {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
    });
    
    // اختبار الأحداث
    const events = ['click', 'mousedown', 'touchstart'];
    events.forEach(eventType => {
        const listeners = getEventListeners ? getEventListeners(medButton) : 'غير متوفر';
        console.log(`🎯 أحداث ${eventType}:`, listeners[eventType] || 'لا توجد');
    });
    
    // محاولة النقر برمجياً
    console.log('🖱️ محاولة النقر برمجياً...');
    try {
        medButton.click();
        console.log('✅ تم النقر بنجاح');
    } catch (error) {
        console.error('❌ فشل النقر:', error);
    }
    
    return true;
}

// دالة لإضافة زر اختبار مؤقت
function addTestButton() {
    const testBtn = document.createElement('button');
    testBtn.textContent = 'اختبار زر med';
    testBtn.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 999999;
        background: #00ff00;
        color: black;
        padding: 10px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
    `;
    
    testBtn.onclick = testMedButton;
    document.body.appendChild(testBtn);
    
    console.log('✅ تم إضافة زر الاختبار في الزاوية اليسرى العلوية');
}

// دالة لمحاكاة النقر على زر med
function simulateClickOnMed() {
    const medButton = document.querySelector('#med-overlay');
    if (medButton) {
        console.log('🖱️ محاكاة النقر على زر med...');
        
        // إنشاء حدث نقر
        const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            view: window
        });
        
        medButton.dispatchEvent(clickEvent);
        console.log('✅ تم إرسال حدث النقر');
    } else {
        console.error('❌ لم يتم العثور على زر med');
    }
}

// تشغيل الاختبار تلقائياً بعد 3 ثوان
setTimeout(() => {
    console.log('🚀 بدء الاختبار التلقائي...');
    testMedButton();
    addTestButton();
}, 3000);

console.log('📝 تم تحميل ملف الاختبار. سيتم تشغيل الاختبار خلال 3 ثوان...');
console.log('💡 يمكنك أيضاً تشغيل الدوال يدوياً:');
console.log('   - testMedButton() للاختبار الشامل');
console.log('   - simulateClickOnMed() لمحاكاة النقر');
console.log('   - addTestButton() لإضافة زر اختبار');
