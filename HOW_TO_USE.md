# 🚀 كيفية استخدام Med YouTube Downloader Pro

## ⚡ الطريقة السريعة (موصى بها)

### 1. من الإضافة:
1. **اذهب إلى YouTube** وافتح أي فيديو
2. **انقر زر "med"** في الزاوية اليمنى العلوية
3. **اختر صيغة** من القائمة
4. **سيتم تحميل ملف JSON** تلقائياً
5. **ضع الملف** في مجلد الإضافة
6. **انقر مرتين على `run_downloader.bat`**

### 2. من سطر الأوامر:
```bash
# فتح الواجهة الرسومية
python youtube_downloader.py --gui

# أو مع رابط مباشر
python youtube_downloader.py --url "https://youtube.com/watch?v=VIDEO_ID" --gui
```

---

## 🔧 الطرق البديلة

### الطريقة 1: ملف Batch (Windows)
1. **انقر مرتين** على `auto_downloader.bat`
2. **اتبع التعليمات** على الشاشة
3. **الصق رابط الفيديو** عند الطلب

### الطريقة 2: نسخ الأمر
1. **انقر زر "med"** في YouTube
2. **اختر صيغة**
3. **انقر "نعم"** لنسخ الأمر
4. **افتح CMD/Terminal**
5. **الصق الأمر** واضغط Enter

### الطريقة 3: ملف البيانات
1. **حمّل ملف `med_video_data.json`** من الإضافة
2. **ضعه في مجلد الإضافة**
3. **شغّل:** `python youtube_downloader.py --auto-check`

---

## 📋 متطلبات النظام

### ✅ مطلوب:
- **Python 3.7+** مثبت ومضاف لـ PATH
- **مكتبة yt-dlp:** `pip install yt-dlp`
- **اتصال إنترنت** مستقر

### 🔍 فحص المتطلبات:
```bash
# فحص Python
python --version

# فحص yt-dlp
python -c "import yt_dlp; print('OK')"

# تشغيل اختبار شامل
python test_system.py
```

---

## 🎯 خطوات التحميل المفصلة

### الخطوة 1: اختيار الفيديو
1. اذهب إلى **YouTube.com**
2. افتح **الفيديو المطلوب**
3. تأكد من أن الفيديو **يعمل بشكل طبيعي**

### الخطوة 2: استخدام الإضافة
1. ابحث عن **زر "med"** في الزاوية اليمنى العلوية
2. **انقر على الزر** - ستظهر نافذة الصيغ
3. **اختر الصيغة المطلوبة** (MP4, MP3, إلخ)

### الخطوة 3: تشغيل البرنامج
#### الطريقة الأسهل:
- **انقر مرتين** على `run_downloader.bat`

#### الطريقة اليدوية:
```bash
# افتح CMD في مجلد الإضافة
cd "C:\path\to\extension\folder"

# شغّل البرنامج
python youtube_downloader.py --gui
```

### الخطوة 4: التحميل
1. **اختر مجلد التحميل** في الواجهة
2. **انقر "تحميل"**
3. **انتظر اكتمال التحميل**

---

## 🔧 حل المشاكل الشائعة

### المشكلة: "Python غير موجود"
**الحل:**
1. حمّل Python من https://python.org/downloads/
2. **مهم:** فعّل "Add Python to PATH" أثناء التثبيت
3. أعد تشغيل CMD وجرب: `python --version`

### المشكلة: "yt-dlp غير مثبت"
**الحل:**
```bash
pip install yt-dlp
# أو
pip install --upgrade yt-dlp
```

### المشكلة: "برنامج Python لا يفتح"
**الحل:**
1. تأكد من وجود ملف `youtube_downloader.py`
2. افتح CMD في نفس مجلد الملف
3. شغّل: `python youtube_downloader.py --gui`

### المشكلة: "فشل التحميل"
**الحل:**
1. تحقق من **اتصال الإنترنت**
2. تأكد من أن **الفيديو ليس خاص**
3. جرب **فيديو آخر**
4. حدّث yt-dlp: `pip install --upgrade yt-dlp`

---

## 💡 نصائح مفيدة

### لأفضل تجربة:
- ✅ **استخدم جودة 720p** للتوازن بين الحجم والجودة
- ✅ **تأكد من مساحة كافية** في مجلد التحميل
- ✅ **أغلق برامج التحميل الأخرى** لتجنب التعارض
- ✅ **استخدم اتصال إنترنت مستقر**

### اختصارات مفيدة:
```bash
# فتح مجلد الإضافة في CMD
cd /d "C:\Users\<USER>\Desktop\augment\extension"

# تشغيل سريع
python youtube_downloader.py --gui

# فحص حالة النظام
python test_system.py
```

---

## 📞 الحصول على المساعدة

### إذا واجهت مشاكل:
1. **راجع ملف `TROUBLESHOOTING.md`**
2. **شغّل `python test_system.py`** للتشخيص
3. **تأكد من تحديث Python وyt-dlp**

### معلومات مفيدة للدعم:
- **إصدار Python:** `python --version`
- **إصدار yt-dlp:** `python -c "import yt_dlp; print(yt_dlp.version.__version__)"`
- **نظام التشغيل:** Windows/Mac/Linux
- **رسالة الخطأ:** انسخ النص كاملاً

---

## 🎉 استمتع بالتحميل!

الآن يمكنك تحميل أي فيديو من YouTube بجودة عالية وصيغ متعددة! 

**تذكر:** احترم حقوق الطبع والنشر واستخدم المحتوى بشكل قانوني. 📝
