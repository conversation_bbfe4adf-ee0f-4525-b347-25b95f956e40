<!DOCTYPE html>
<html>
<head>
    <title>اختبار بناء الجملة - Med Extension</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>اختبار بناء الجملة</h1>
    <div id="test-results"></div>
    
    <script>
        // اختبار تحميل ملف content.js
        function testSyntax() {
            const results = document.getElementById('test-results');
            
            try {
                // محاولة تحميل وتنفيذ الكود
                const script = document.createElement('script');
                script.src = 'content.js';
                script.onload = function() {
                    results.innerHTML = '<p style="color: green;">✅ تم تحميل content.js بنجاح - لا توجد أخطاء في بناء الجملة!</p>';
                };
                script.onerror = function() {
                    results.innerHTML = '<p style="color: red;">❌ خطأ في تحميل content.js</p>';
                };
                document.head.appendChild(script);
                
            } catch (error) {
                results.innerHTML = '<p style="color: red;">❌ خطأ في بناء الجملة: ' + error.message + '</p>';
            }
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        window.onload = testSyntax;
    </script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
            background: #f5f5f5;
        }
        
        h1 {
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        
        #test-results {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</body>
</html>
