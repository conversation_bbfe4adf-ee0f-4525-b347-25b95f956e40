#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Med YouTube Downloader - برنامج تحميل فيديوهات YouTube
يدعم صيغ وجودات متعددة مع واجهة سطر الأوامر
"""

import sys
import json
import os
import argparse
from pathlib import Path
import subprocess
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
import time

try:
    import yt_dlp
except ImportError:
    print("خطأ: مكتبة yt-dlp غير مثبتة")
    print("قم بتثبيتها باستخدام: pip install yt-dlp")
    sys.exit(1)

class YouTubeDownloader:
    def __init__(self):
        self.download_path = str(Path.home() / "Downloads")
        self.progress_callback = None
        
    def get_video_info(self, url):
        """استخراج معلومات الفيديو والصيغ المتاحة"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': False,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                # استخراج الصيغ المتاحة
                formats = []
                if 'formats' in info:
                    for f in info['formats']:
                        if f.get('vcodec') != 'none' or f.get('acodec') != 'none':
                            format_info = {
                                'format_id': f.get('format_id', ''),
                                'ext': f.get('ext', ''),
                                'quality': f.get('format_note', ''),
                                'resolution': f.get('resolution', 'audio only' if f.get('vcodec') == 'none' else 'unknown'),
                                'filesize': f.get('filesize', 0),
                                'vcodec': f.get('vcodec', 'none'),
                                'acodec': f.get('acodec', 'none'),
                                'fps': f.get('fps', 0),
                                'tbr': f.get('tbr', 0)
                            }
                            formats.append(format_info)
                
                return {
                    'title': info.get('title', 'Unknown'),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'Unknown'),
                    'view_count': info.get('view_count', 0),
                    'formats': formats
                }
                
        except Exception as e:
            return {'error': str(e)}
    
    def download_video(self, url, format_id, output_path=None):
        """تحميل الفيديو بالصيغة المحددة"""
        try:
            if output_path:
                self.download_path = output_path
            
            ydl_opts = {
                'format': format_id,
                'outtmpl': os.path.join(self.download_path, '%(title)s.%(ext)s'),
                'progress_hooks': [self._progress_hook] if self.progress_callback else [],
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
                
            return {'success': True, 'message': 'تم التحميل بنجاح'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _progress_hook(self, d):
        """معالج تقدم التحميل"""
        if self.progress_callback and d['status'] == 'downloading':
            percent = d.get('_percent_str', '0%')
            speed = d.get('_speed_str', '0KB/s')
            self.progress_callback(percent, speed)

class DownloaderGUI:
    def __init__(self, url=None, format_data=None):
        self.downloader = YouTubeDownloader()
        self.url = url
        self.format_data = format_data
        self.root = tk.Tk()
        self.setup_gui()
        
    def setup_gui(self):
        """إعداد واجهة المستخدم"""
        self.root.title("Med YouTube Downloader")
        self.root.geometry("600x500")
        self.root.configure(bg='#f0f0f0')
        
        # إعداد الخط العربي
        try:
            self.root.option_add('*Font', 'Arial 10')
        except:
            pass
        
        # عنوان البرنامج
        title_label = tk.Label(
            self.root, 
            text="🎬 Med YouTube Downloader", 
            font=('Arial', 16, 'bold'),
            bg='#f0f0f0',
            fg='#333'
        )
        title_label.pack(pady=10)
        
        # إطار معلومات الفيديو
        info_frame = tk.LabelFrame(self.root, text="معلومات الفيديو", font=('Arial', 12, 'bold'))
        info_frame.pack(fill='x', padx=10, pady=5)
        
        self.info_text = tk.Text(info_frame, height=4, wrap=tk.WORD, font=('Arial', 9))
        self.info_text.pack(fill='x', padx=5, pady=5)
        
        # إطار الصيغ
        formats_frame = tk.LabelFrame(self.root, text="الصيغ المتاحة", font=('Arial', 12, 'bold'))
        formats_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # جدول الصيغ
        columns = ('الصيغة', 'الجودة', 'الدقة', 'الحجم', 'معرف الصيغة')
        self.formats_tree = ttk.Treeview(formats_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.formats_tree.heading(col, text=col)
            self.formats_tree.column(col, width=100)
        
        scrollbar = ttk.Scrollbar(formats_frame, orient='vertical', command=self.formats_tree.yview)
        self.formats_tree.configure(yscrollcommand=scrollbar.set)
        
        self.formats_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        scrollbar.pack(side='right', fill='y')
        
        # إطار التحكم
        control_frame = tk.Frame(self.root, bg='#f0f0f0')
        control_frame.pack(fill='x', padx=10, pady=5)
        
        # زر اختيار مجلد
        self.path_button = tk.Button(
            control_frame, 
            text="📁 اختيار مجلد التحميل", 
            command=self.select_download_path,
            font=('Arial', 10),
            bg='#4CAF50',
            fg='white',
            relief='flat',
            padx=20
        )
        self.path_button.pack(side='left', padx=5)
        
        # زر التحميل
        self.download_button = tk.Button(
            control_frame, 
            text="⬇️ تحميل", 
            command=self.start_download,
            font=('Arial', 10, 'bold'),
            bg='#2196F3',
            fg='white',
            relief='flat',
            padx=20
        )
        self.download_button.pack(side='right', padx=5)
        
        # شريط التقدم
        self.progress_var = tk.StringVar(value="جاهز للتحميل")
        self.progress_label = tk.Label(self.root, textvariable=self.progress_var, font=('Arial', 9))
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress_bar.pack(fill='x', padx=10, pady=5)
        
        # تحميل البيانات إذا كانت متوفرة
        if self.url and self.format_data:
            self.load_video_data()
    
    def load_video_data(self):
        """تحميل بيانات الفيديو"""
        try:
            data = json.loads(self.format_data)
            
            # عرض معلومات الفيديو
            info_text = f"العنوان: {data.get('title', 'غير معروف')}\n"
            info_text += f"المدة: {self.format_duration(data.get('duration', 0))}\n"
            info_text += f"القناة: {data.get('uploader', 'غير معروف')}\n"
            info_text += f"المشاهدات: {data.get('view_count', 0):,}"
            
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info_text)
            
            # عرض الصيغ
            for format_info in data.get('formats', []):
                self.formats_tree.insert('', 'end', values=(
                    format_info.get('ext', ''),
                    format_info.get('quality', ''),
                    format_info.get('resolution', ''),
                    self.format_filesize(format_info.get('filesize', 0)),
                    format_info.get('format_id', '')
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات الفيديو: {str(e)}")
    
    def format_duration(self, seconds):
        """تنسيق مدة الفيديو"""
        if not seconds:
            return "غير معروف"
        
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def format_filesize(self, size):
        """تنسيق حجم الملف"""
        if not size:
            return "غير معروف"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    
    def select_download_path(self):
        """اختيار مجلد التحميل"""
        path = filedialog.askdirectory(initialdir=self.downloader.download_path)
        if path:
            self.downloader.download_path = path
            self.path_button.config(text=f"📁 {os.path.basename(path)}")
    
    def start_download(self):
        """بدء التحميل"""
        selection = self.formats_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صيغة للتحميل")
            return
        
        # الحصول على معرف الصيغة المحددة
        item = self.formats_tree.item(selection[0])
        format_id = item['values'][4]  # معرف الصيغة
        
        # بدء التحميل في خيط منفصل
        self.download_button.config(state='disabled')
        self.progress_bar.start()
        self.progress_var.set("جاري التحميل...")
        
        def download_thread():
            self.downloader.progress_callback = self.update_progress
            result = self.downloader.download_video(self.url, format_id)
            
            self.root.after(0, lambda: self.download_complete(result))
        
        threading.Thread(target=download_thread, daemon=True).start()
    
    def update_progress(self, percent, speed):
        """تحديث شريط التقدم"""
        self.progress_var.set(f"جاري التحميل... {percent} - {speed}")
    
    def download_complete(self, result):
        """إكمال التحميل"""
        self.progress_bar.stop()
        self.download_button.config(state='normal')
        
        if result.get('success'):
            self.progress_var.set("تم التحميل بنجاح!")
            messagebox.showinfo("نجح", "تم تحميل الفيديو بنجاح!")
        else:
            self.progress_var.set("فشل التحميل")
            messagebox.showerror("خطأ", f"فشل التحميل: {result.get('error', 'خطأ غير معروف')}")
    
    def run(self):
        """تشغيل الواجهة"""
        self.root.mainloop()

def main():
    parser = argparse.ArgumentParser(description='Med YouTube Downloader')
    parser.add_argument('--url', help='رابط الفيديو')
    parser.add_argument('--get-formats', action='store_true', help='الحصول على الصيغ المتاحة')
    parser.add_argument('--format-data', help='بيانات الصيغ (JSON)')
    parser.add_argument('--gui', action='store_true', help='تشغيل الواجهة الرسومية')
    
    args = parser.parse_args()
    
    if args.gui or (args.url and args.format_data):
        # تشغيل الواجهة الرسومية
        app = DownloaderGUI(args.url, args.format_data)
        app.run()
    elif args.url and args.get_formats:
        # الحصول على الصيغ وطباعتها
        downloader = YouTubeDownloader()
        info = downloader.get_video_info(args.url)
        print(json.dumps(info, ensure_ascii=False, indent=2))
    else:
        print("الاستخدام:")
        print("  python youtube_downloader.py --url URL --get-formats")
        print("  python youtube_downloader.py --url URL --format-data JSON --gui")

if __name__ == "__main__":
    main()
