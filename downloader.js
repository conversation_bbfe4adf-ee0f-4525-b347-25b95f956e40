// وحدة تحميل فيديوهات YouTube محسنة
class YouTubeDownloader {
    constructor() {
        this.downloadServices = [
            'https://www.y2mate.com',
            'https://ytmp3.cc',
            'https://www.onlinevideoconverter.com'
        ];
    }

    // دالة لتحميل الفيديو باستخدام خدمة خارجية
    async downloadVideo(videoId, videoTitle) {
        try {
            // إنشاء رابط التحميل
            const downloadUrl = this.createDownloadUrl(videoId);
            
            // فتح نافذة جديدة لخدمة التحميل
            const downloadWindow = window.open(downloadUrl, '_blank');
            
            if (!downloadWindow) {
                throw new Error('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة لهذا الموقع.');
            }
            
            return { success: true, message: 'تم فتح صفحة التحميل' };
            
        } catch (error) {
            console.error('خطأ في التحميل:', error);
            return { success: false, error: error.message };
        }
    }

    // إنشاء رابط التحميل
    createDownloadUrl(videoId) {
        const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
        
        // استخدام خدمة Y2mate كخيار افتراضي
        return `https://www.y2mate.com/youtube/${videoId}`;
    }

    // دالة بديلة لتحميل مباشر (تتطلب API خاص)
    async directDownload(videoId, videoTitle, quality = '720p') {
        try {
            // هذه الطريقة تتطلب API متخصص أو خادم وسيط
            // للتبسيط، سنستخدم خدمة خارجية
            
            const response = await fetch(`https://api.example.com/download`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    videoId: videoId,
                    quality: quality,
                    format: 'mp4'
                })
            });

            if (!response.ok) {
                throw new Error('فشل في الحصول على رابط التحميل');
            }

            const data = await response.json();
            return data.downloadUrl;

        } catch (error) {
            console.error('خطأ في التحميل المباشر:', error);
            throw error;
        }
    }

    // دالة لاختيار مجلد التحميل
    async selectDownloadFolder() {
        try {
            if ('showDirectoryPicker' in window) {
                // استخدام File System Access API (متوفر في المتصفحات الحديثة)
                const directoryHandle = await window.showDirectoryPicker();
                return directoryHandle;
            } else {
                // استخدام طريقة بديلة للمتصفحات القديمة
                return null;
            }
        } catch (error) {
            console.error('خطأ في اختيار المجلد:', error);
            return null;
        }
    }

    // دالة لحفظ الملف في المجلد المحدد
    async saveToFolder(directoryHandle, fileName, blob) {
        try {
            const fileHandle = await directoryHandle.getFileHandle(fileName, {
                create: true
            });
            
            const writable = await fileHandle.createWritable();
            await writable.write(blob);
            await writable.close();
            
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الملف:', error);
            return false;
        }
    }
}

// تصدير الكلاس للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = YouTubeDownloader;
} else {
    window.YouTubeDownloader = YouTubeDownloader;
}
