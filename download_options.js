// خيارات التحميل المتقدمة لفيديوهات YouTube
class DownloadOptions {
    constructor() {
        this.services = [
            {
                name: 'Y2mate',
                url: 'https://www.y2mate.com/youtube/',
                description: 'خدمة سريعة وموثوقة',
                quality: ['144p', '360p', '720p', '1080p'],
                formats: ['MP4', 'MP3']
            },
            {
                name: 'ClipConverter',
                url: 'https://www.clipconverter.cc/2/',
                description: 'يدعم صيغ متعددة',
                quality: ['360p', '720p', '1080p'],
                formats: ['MP4', 'AVI', 'MP3', 'M4A']
            },
            {
                name: 'OnlineVideoConverter',
                url: 'https://www.onlinevideoconverter.com/youtube-converter?url=',
                description: 'سهل الاستخدام',
                quality: ['360p', '720p', '1080p'],
                formats: ['MP4', 'MP3', 'AVI']
            },
            {
                name: 'YTMP3',
                url: 'https://ytmp3.cc/youtube-to-mp4/',
                description: 'متخصص في MP3 و MP4',
                quality: ['360p', '720p'],
                formats: ['MP4', 'MP3']
            }
        ];
    }

    // إنشاء نافذة اختيار خدمة التحميل
    showDownloadOptions(videoId, videoTitle, videoUrl) {
        return new Promise((resolve) => {
            // إنشاء نافذة منبثقة مخصصة
            const modal = this.createModal(videoId, videoTitle, videoUrl);
            document.body.appendChild(modal);
            
            // إضافة أحداث الأزرار
            this.addModalEvents(modal, resolve);
        });
    }

    // إنشاء النافذة المنبثقة
    createModal(videoId, videoTitle, videoUrl) {
        const modal = document.createElement('div');
        modal.id = 'med-download-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999999;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            background: white;
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            max-height: 80%;
            overflow-y: auto;
            direction: rtl;
            text-align: right;
        `;

        content.innerHTML = `
            <h2 style="color: #333; margin-bottom: 15px;">تحميل الفيديو</h2>
            <p style="color: #666; margin-bottom: 20px; font-size: 14px;">
                <strong>العنوان:</strong> ${videoTitle.substring(0, 50)}...
            </p>
            <p style="color: #666; margin-bottom: 20px; font-size: 12px;">
                اختر خدمة التحميل المفضلة:
            </p>
            <div id="services-list">
                ${this.services.map((service, index) => `
                    <div class="service-option" data-index="${index}" style="
                        border: 2px solid #ddd;
                        padding: 15px;
                        margin-bottom: 10px;
                        border-radius: 8px;
                        cursor: pointer;
                        transition: all 0.3s;
                    ">
                        <h3 style="color: #333; margin: 0 0 5px 0; font-size: 16px;">${service.name}</h3>
                        <p style="color: #666; margin: 0 0 8px 0; font-size: 12px;">${service.description}</p>
                        <div style="font-size: 11px; color: #888;">
                            <span>الجودة: ${service.quality.join(', ')}</span> | 
                            <span>الصيغ: ${service.formats.join(', ')}</span>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button id="cancel-download" style="
                    background: #ccc;
                    color: #333;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin-left: 10px;
                ">إلغاء</button>
            </div>
        `;

        modal.appendChild(content);
        return modal;
    }

    // إضافة أحداث النافذة
    addModalEvents(modal, resolve) {
        // حدث النقر على خدمة
        const serviceOptions = modal.querySelectorAll('.service-option');
        serviceOptions.forEach(option => {
            option.addEventListener('click', () => {
                const index = parseInt(option.dataset.index);
                const service = this.services[index];
                
                // تمييز الخيار المحدد
                serviceOptions.forEach(opt => opt.style.borderColor = '#ddd');
                option.style.borderColor = '#ff0000';
                option.style.backgroundColor = '#fff5f5';
                
                // فتح خدمة التحميل بعد ثانية واحدة
                setTimeout(() => {
                    this.openDownloadService(service, modal.videoId, modal.videoUrl);
                    document.body.removeChild(modal);
                    resolve(true);
                }, 1000);
            });

            // تأثير التمرير
            option.addEventListener('mouseenter', () => {
                if (option.style.borderColor !== 'rgb(255, 0, 0)') {
                    option.style.borderColor = '#999';
                    option.style.backgroundColor = '#f9f9f9';
                }
            });

            option.addEventListener('mouseleave', () => {
                if (option.style.borderColor !== 'rgb(255, 0, 0)') {
                    option.style.borderColor = '#ddd';
                    option.style.backgroundColor = 'white';
                }
            });
        });

        // حدث الإلغاء
        const cancelBtn = modal.querySelector('#cancel-download');
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(modal);
            resolve(false);
        });

        // إغلاق عند النقر خارج النافذة
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
                resolve(false);
            }
        });

        // حفظ بيانات الفيديو في النافذة
        modal.videoId = arguments[3] || '';
        modal.videoUrl = arguments[4] || '';
    }

    // فتح خدمة التحميل
    openDownloadService(service, videoId, videoUrl) {
        let downloadUrl;
        
        if (service.name === 'Y2mate') {
            downloadUrl = service.url + videoId;
        } else if (service.name === 'OnlineVideoConverter') {
            downloadUrl = service.url + encodeURIComponent(videoUrl);
        } else {
            downloadUrl = service.url;
        }
        
        // فتح في تبويب جديد
        window.open(downloadUrl, '_blank');
    }
}

// تصدير الكلاس
if (typeof window !== 'undefined') {
    window.DownloadOptions = DownloadOptions;
}
