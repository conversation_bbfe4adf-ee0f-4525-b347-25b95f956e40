@echo off
chcp 65001 >nul
title Med YouTube Downloader Pro

echo.
echo 🎬 Med YouTube Downloader Pro
echo ================================
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo.
    echo 📥 حمّل Python من: https://python.org/downloads/
    echo ⚠️ تأكد من تفعيل "Add Python to PATH"
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM تثبيت المتطلبات إذا لم تكن موجودة
python -c "import yt_dlp" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت yt-dlp...
    pip install yt-dlp
)

echo ✅ جميع المتطلبات متوفرة
echo.

REM تشغيل البرنامج
echo 🚀 تشغيل برنامج التحميل...
python youtube_downloader.py --auto-check

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ
    echo 💡 جرب: python youtube_downloader.py --gui
)

echo.
pause
