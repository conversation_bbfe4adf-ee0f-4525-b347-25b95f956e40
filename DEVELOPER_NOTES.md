# ملاحظات المطور - Med YouTube Downloader

## هيكل المشروع

```
├── manifest.json          # ملف التكوين الأساسي للإضافة
├── content.js            # السكريبت الذي يعمل على صفحات YouTube
├── background.js         # خدمة الخلفية للتعامل مع التحميل
├── downloader.js         # وحدة التحميل المحسنة
├── styles.css           # ملف التنسيق للزر
├── README.md            # دليل المستخدم
└── DEVELOPER_NOTES.md   # هذا الملف
```

## كيفية عمل الإضافة

### 1. Content Script (content.js)
- يتم حقنه في صفحات YouTube
- يبحث عن مشغل الفيديو (`#movie_player`)
- ينشئ زر "med" قابل للنقر
- يستمع لأحداث النقر ويرسل رسائل إلى background script

### 2. Background Script (background.js)
- يستمع للرسائل من content script
- يتعامل مع عملية التحميل
- يستخدم Chrome Downloads API
- يرسل إشعارات للمستخدم

### 3. Downloader Module (downloader.js)
- وحدة مساعدة للتحميل
- تحتوي على خيارات متقدمة للتحميل
- يمكن توسيعها لاستخدام APIs مختلفة

## الصلاحيات المطلوبة

- `activeTab`: للوصول إلى التبويب النشط
- `downloads`: لتحميل الملفات
- `storage`: لحفظ الإعدادات
- `notifications`: لإرسال الإشعارات
- `host_permissions`: للوصول إلى YouTube

## التحسينات المقترحة

### 1. تحسين التحميل
```javascript
// إضافة خيارات جودة مختلفة
const qualityOptions = ['144p', '360p', '720p', '1080p'];

// استخدام APIs متخصصة
const ytdlAPI = 'https://api.youtube-dl.org/';
```

### 2. واجهة مستخدم محسنة
```css
/* إضافة قائمة منسدلة للخيارات */
.med-dropdown {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 4px;
    padding: 5px;
}
```

### 3. إعدادات متقدمة
- اختيار مجلد التحميل الافتراضي
- تحديد جودة التحميل المفضلة
- تفعيل/إلغاء الإشعارات

## استكشاف الأخطاء

### مشاكل شائعة:

1. **الزر لا يظهر:**
   - تحقق من تحميل content script
   - تأكد من وجود مشغل الفيديو

2. **التحميل لا يعمل:**
   - تحقق من صلاحيات التحميل
   - تأكد من عمل background script

3. **خطأ في الصلاحيات:**
   - تحقق من manifest.json
   - أعد تحميل الإضافة

### تسجيل الأخطاء:
```javascript
// في content.js
console.log('Med Extension: زر تم إنشاؤه');

// في background.js
console.error('خطأ في التحميل:', error);
```

## APIs المستخدمة

### Chrome Extension APIs:
- `chrome.runtime.sendMessage()` - إرسال الرسائل
- `chrome.downloads.download()` - تحميل الملفات
- `chrome.notifications.create()` - إنشاء الإشعارات

### Web APIs:
- `MutationObserver` - مراقبة تغييرات DOM
- `URLSearchParams` - استخراج معرف الفيديو
- `fetch()` - طلبات HTTP (للتحميل المتقدم)

## الأمان

### اعتبارات الأمان:
1. تنظيف أسماء الملفات من الأحرف الخطيرة
2. التحقق من صحة معرف الفيديو
3. استخدام HTTPS فقط للتحميل
4. عدم تخزين بيانات حساسة

### مثال على التنظيف:
```javascript
function sanitizeFileName(fileName) {
    return fileName.replace(/[<>:"/\\|?*]/g, '_').substring(0, 100);
}
```

## التطوير المستقبلي

### ميزات مقترحة:
1. دعم تحميل قوائم التشغيل
2. تحويل إلى صيغ مختلفة (MP3, MP4, etc.)
3. جدولة التحميل
4. إحصائيات التحميل
5. مزامنة الإعدادات عبر الأجهزة

### تحسينات الأداء:
1. تحميل متوازي للملفات
2. استئناف التحميل المتوقف
3. ضغط الملفات
4. تحسين استهلاك الذاكرة
