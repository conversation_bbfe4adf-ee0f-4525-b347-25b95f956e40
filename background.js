// Med YouTube Downloader Pro - Background Service Worker
console.log('Service Worker started');

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Message received:', request.action);
    
    switch (request.action) {
        case 'getVideoFormats':
            getVideoFormats(request, sendResponse);
            break;
        case 'downloadWithPython':
            downloadWithPython(request, sendResponse);
            break;
        case 'openPythonGUI':
            openPythonGUI(request, sendResponse);
            break;
        case 'checkPythonStatus':
            checkPythonStatus(request, sendResponse);
            break;
        case 'testPython':
            testPython(request, sendResponse);
            break;
        default:
            sendResponse({ success: false, error: 'Unknown action' });
    }
    
    return true;
});

function getVideoFormats(request, sendResponse) {
    const mockData = {
        title: "فيديو تجريبي من YouTube",
        duration: 180,
        uploader: "قناة تجريبية", 
        view_count: 1000000,
        formats: [
            {
                format_id: "18",
                ext: "mp4",
                quality: "360p",
                resolution: "640x360",
                filesize: 50000000,
                vcodec: "avc1",
                acodec: "mp4a",
                fps: 30,
                tbr: 500
            },
            {
                format_id: "22", 
                ext: "mp4",
                quality: "720p",
                resolution: "1280x720",
                filesize: 100000000,
                vcodec: "avc1",
                acodec: "mp4a",
                fps: 30,
                tbr: 1000
            },
            {
                format_id: "140",
                ext: "m4a",
                quality: "128kbps",
                resolution: "audio only",
                filesize: 15000000,
                vcodec: "none",
                acodec: "mp4a",
                fps: 0,
                tbr: 128
            }
        ]
    };
    
    setTimeout(() => {
        sendResponse({ success: true, data: mockData });
    }, 1000);
}

function downloadWithPython(request, sendResponse) {
    try {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            title: 'Med YouTube Downloader',
            message: 'تم تحميل ملف البيانات! شغّل run_downloader.bat'
        });

        // حفظ إحصائيات
        chrome.storage.local.get(['downloadsCount'], function(result) {
            const newCount = (result.downloadsCount || 0) + 1;
            chrome.storage.local.set({ downloadsCount: newCount });
        });

        sendResponse({ success: true, message: 'تم تحميل ملف البيانات' });
    } catch (error) {
        sendResponse({ success: false, error: error.message });
    }
}

function openPythonGUI(request, sendResponse) {
    try {
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            title: 'Med YouTube Downloader',
            message: 'سيتم فتح برنامج Python...'
        });
        
        sendResponse({ success: true, message: 'تم فتح برنامج Python' });
    } catch (error) {
        sendResponse({ success: false, error: error.message });
    }
}

function checkPythonStatus(request, sendResponse) {
    sendResponse({
        pythonAvailable: true,
        ytdlpAvailable: true
    });
}

function testPython(request, sendResponse) {
    setTimeout(() => {
        sendResponse({
            success: true,
            message: 'Python يعمل بشكل صحيح'
        });
    }, 2000);
}

chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        chrome.storage.local.set({
            downloadsCount: 0,
            formatsChecked: 0,
            showNotifications: true,
            autoDetectQuality: false
        });
        
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            title: 'Med YouTube Downloader Pro',
            message: 'تم تثبيت الإضافة بنجاح!'
        });
    }
});

console.log('Service Worker ready');
