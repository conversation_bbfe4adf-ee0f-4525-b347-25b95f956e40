// Background script لتحميل فيديوهات YouTube مع دعم Python
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    switch (request.action) {
        case 'getVideoFormats':
            getVideoFormats(request, sendResponse);
            break;
        case 'downloadWithPython':
            downloadWithPython(request, sendResponse);
            break;
        case 'openPythonGUI':
            openPythonGUI(request, sendResponse);
            break;
        case 'downloadVideo':
            handleVideoDownload(request, sendResponse);
            break;
    }
    return true; // للحفاظ على قناة الاتصال مفتوحة
});

// دالة للحصول على صيغ الفيديو من برنامج Python
async function getVideoFormats(request, sendResponse) {
    try {
        const { videoUrl } = request;

        // تشغيل برنامج Python للحصول على الصيغ
        const pythonPath = await getPythonPath();
        const scriptPath = await getScriptPath();

        if (!pythonPath || !scriptPath) {
            sendResponse({
                success: false,
                error: 'برنامج Python أو السكريبت غير موجود'
            });
            return;
        }

        // تنفيذ الأمر للحصول على الصيغ
        const command = `"${pythonPath}" "${scriptPath}" --url "${videoUrl}" --get-formats`;

        // محاكاة استدعاء Python (في التطبيق الحقيقي نحتاج Native Messaging)
        // هنا سنستخدم طريقة بديلة
        const result = await simulatePythonCall(command);

        if (result.success) {
            sendResponse({ success: true, data: result.data });
        } else {
            sendResponse({ success: false, error: result.error });
        }

    } catch (error) {
        console.error('خطأ في الحصول على صيغ الفيديو:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// دالة لتحميل الفيديو باستخدام Python
async function downloadWithPython(request, sendResponse) {
    try {
        const { videoUrl, formatId, formatInfo, videoData } = request;

        const pythonPath = await getPythonPath();
        const scriptPath = await getScriptPath();

        if (!pythonPath || !scriptPath) {
            sendResponse({
                success: false,
                error: 'برنامج Python أو السكريپت غير موجود'
            });
            return;
        }

        // تشغيل برنامج Python للتحميل
        const formatData = JSON.stringify(videoData);
        const command = `"${pythonPath}" "${scriptPath}" --url "${videoUrl}" --format-data '${formatData}' --gui`;

        const result = await simulatePythonCall(command);

        if (result.success) {
            sendResponse({ success: true, message: 'تم بدء التحميل' });
        } else {
            sendResponse({ success: false, error: result.error });
        }

    } catch (error) {
        console.error('خطأ في تحميل الفيديو:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// دالة لفتح واجهة Python الرسومية
async function openPythonGUI(request, sendResponse) {
    try {
        const { videoUrl, videoData } = request;

        const pythonPath = await getPythonPath();
        const scriptPath = await getScriptPath();

        if (!pythonPath || !scriptPath) {
            // إذا لم يكن Python متوفر، اعرض تعليمات التثبيت
            showPythonInstallInstructions();
            sendResponse({
                success: false,
                error: 'برنامج Python غير مثبت'
            });
            return;
        }

        // تشغيل واجهة Python الرسومية
        const formatData = JSON.stringify(videoData);
        const command = `"${pythonPath}" "${scriptPath}" --url "${videoUrl}" --format-data '${formatData}' --gui`;

        const result = await simulatePythonCall(command);

        if (result.success) {
            sendResponse({ success: true, message: 'تم فتح برنامج Python' });
        } else {
            sendResponse({ success: false, error: result.error });
        }

    } catch (error) {
        console.error('خطأ في فتح برنامج Python:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// دالة للحصول على مسار Python
async function getPythonPath() {
    // في التطبيق الحقيقي، نحتاج للبحث عن Python في النظام
    // هنا سنعيد مسار افتراضي
    const possiblePaths = [
        'python',
        'python3',
        'C:\\Python39\\python.exe',
        'C:\\Python310\\python.exe',
        'C:\\Python311\\python.exe',
        '/usr/bin/python3',
        '/usr/local/bin/python3'
    ];

    // في التطبيق الحقيقي، نختبر كل مسار
    return possiblePaths[0]; // إرجاع أول مسار كمثال
}

// دالة للحصول على مسار السكريپت
async function getScriptPath() {
    // في التطبيق الحقيقي، نحصل على مسار الإضافة
    // ونبحث عن ملف youtube_downloader.py
    return 'youtube_downloader.py';
}

// دالة لمحاكاة استدعاء Python (للاختبار)
async function simulatePythonCall(command) {
    try {
        // في التطبيق الحقيقي، نستخدم Native Messaging أو subprocess
        // هنا سنعيد بيانات تجريبية

        console.log('تنفيذ الأمر:', command);

        // محاكاة بيانات الفيديو
        const mockVideoData = {
            title: "فيديو تجريبي من YouTube",
            duration: 180,
            uploader: "قناة تجريبية",
            view_count: 1000000,
            formats: [
                {
                    format_id: "18",
                    ext: "mp4",
                    quality: "360p",
                    resolution: "640x360",
                    filesize: 50000000,
                    vcodec: "avc1",
                    acodec: "mp4a",
                    fps: 30,
                    tbr: 500
                },
                {
                    format_id: "22",
                    ext: "mp4",
                    quality: "720p",
                    resolution: "1280x720",
                    filesize: 100000000,
                    vcodec: "avc1",
                    acodec: "mp4a",
                    fps: 30,
                    tbr: 1000
                },
                {
                    format_id: "140",
                    ext: "m4a",
                    quality: "128kbps",
                    resolution: "audio only",
                    filesize: 15000000,
                    vcodec: "none",
                    acodec: "mp4a",
                    fps: 0,
                    tbr: 128
                }
            ]
        };

        return { success: true, data: mockVideoData };

    } catch (error) {
        return { success: false, error: error.message };
    }
}

// دالة لعرض تعليمات تثبيت Python
function showPythonInstallInstructions() {
    chrome.notifications.create({
        type: 'basic',
        iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        title: 'Med YouTube Downloader',
        message: 'يرجى تثبيت Python وتشغيل setup.py لتفعيل التحميل المتقدم'
    });
}

async function handleVideoDownload(request, sendResponse) {
    try {
        const { videoId, videoTitle, videoUrl } = request;
        
        // تنظيف اسم الملف
        const sanitizedTitle = sanitizeFileName(videoTitle);
        const fileName = `${sanitizedTitle}_${videoId}.mp4`;
        
        // إنشاء رابط التحميل (استخدام خدمة تحميل خارجية)
        const downloadUrl = await getDownloadUrl(videoId, videoUrl);
        
        if (!downloadUrl) {
            sendResponse({ success: false, error: 'لا يمكن الحصول على رابط التحميل' });
            return;
        }
        
        // بدء التحميل
        chrome.downloads.download({
            url: downloadUrl,
            filename: fileName,
            saveAs: true // يطلب من المستخدم اختيار مجلد الحفظ
        }, (downloadId) => {
            if (chrome.runtime.lastError) {
                console.error('خطأ في التحميل:', chrome.runtime.lastError);
                sendResponse({ success: false, error: chrome.runtime.lastError.message });
            } else {
                console.log('بدأ التحميل بمعرف:', downloadId);
                sendResponse({ success: true, downloadId: downloadId });
            }
        });
        
    } catch (error) {
        console.error('خطأ في معالجة التحميل:', error);
        sendResponse({ success: false, error: error.message });
    }
}

async function getDownloadUrl(videoId, videoUrl) {
    try {
        // استخدام خدمة تحميل مجانية (مثال)
        // ملاحظة: هذا مثال بسيط، قد تحتاج لاستخدام API أكثر تطوراً
        
        // الطريقة 1: استخدام خدمة خارجية
        const downloadServices = [
            `https://www.y2mate.com/youtube/${videoId}`,
            `https://ytmp3.cc/youtube-to-mp4/${videoId}`,
            `https://www.onlinevideoconverter.com/youtube-converter?url=${encodeURIComponent(videoUrl)}`
        ];
        
        // للتبسيط، سنعيد رابط مباشر لصفحة التحميل
        // في التطبيق الحقيقي، ستحتاج لاستخدام API متخصص
        return downloadServices[0];
        
    } catch (error) {
        console.error('خطأ في الحصول على رابط التحميل:', error);
        return null;
    }
}

function sanitizeFileName(fileName) {
    return fileName.replace(/[<>:"/\\|?*]/g, '_').substring(0, 100);
}

// مراقبة حالة التحميل
chrome.downloads.onChanged.addListener((downloadDelta) => {
    if (downloadDelta.state && downloadDelta.state.current === 'complete') {
        console.log('تم إكمال التحميل:', downloadDelta.id);
        // يمكن إضافة إشعار هنا
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            title: 'Med YouTube Downloader',
            message: 'تم تحميل الفيديو بنجاح!'
        });
    } else if (downloadDelta.state && downloadDelta.state.current === 'interrupted') {
        console.log('فشل التحميل:', downloadDelta.id);
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            title: 'Med YouTube Downloader',
            message: 'فشل في تحميل الفيديو'
        });
    }
});
