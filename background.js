// Background script لتحميل فيديوهات YouTube
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'downloadVideo') {
        handleVideoDownload(request, sendResponse);
        return true; // للحفاظ على قناة الاتصال مفتوحة
    }
});

async function handleVideoDownload(request, sendResponse) {
    try {
        const { videoId, videoTitle, videoUrl } = request;
        
        // تنظيف اسم الملف
        const sanitizedTitle = sanitizeFileName(videoTitle);
        const fileName = `${sanitizedTitle}_${videoId}.mp4`;
        
        // إنشاء رابط التحميل (استخدام خدمة تحميل خارجية)
        const downloadUrl = await getDownloadUrl(videoId, videoUrl);
        
        if (!downloadUrl) {
            sendResponse({ success: false, error: 'لا يمكن الحصول على رابط التحميل' });
            return;
        }
        
        // بدء التحميل
        chrome.downloads.download({
            url: downloadUrl,
            filename: fileName,
            saveAs: true // يطلب من المستخدم اختيار مجلد الحفظ
        }, (downloadId) => {
            if (chrome.runtime.lastError) {
                console.error('خطأ في التحميل:', chrome.runtime.lastError);
                sendResponse({ success: false, error: chrome.runtime.lastError.message });
            } else {
                console.log('بدأ التحميل بمعرف:', downloadId);
                sendResponse({ success: true, downloadId: downloadId });
            }
        });
        
    } catch (error) {
        console.error('خطأ في معالجة التحميل:', error);
        sendResponse({ success: false, error: error.message });
    }
}

async function getDownloadUrl(videoId, videoUrl) {
    try {
        // استخدام خدمة تحميل مجانية (مثال)
        // ملاحظة: هذا مثال بسيط، قد تحتاج لاستخدام API أكثر تطوراً
        
        // الطريقة 1: استخدام خدمة خارجية
        const downloadServices = [
            `https://www.y2mate.com/youtube/${videoId}`,
            `https://ytmp3.cc/youtube-to-mp4/${videoId}`,
            `https://www.onlinevideoconverter.com/youtube-converter?url=${encodeURIComponent(videoUrl)}`
        ];
        
        // للتبسيط، سنعيد رابط مباشر لصفحة التحميل
        // في التطبيق الحقيقي، ستحتاج لاستخدام API متخصص
        return downloadServices[0];
        
    } catch (error) {
        console.error('خطأ في الحصول على رابط التحميل:', error);
        return null;
    }
}

function sanitizeFileName(fileName) {
    return fileName.replace(/[<>:"/\\|?*]/g, '_').substring(0, 100);
}

// مراقبة حالة التحميل
chrome.downloads.onChanged.addListener((downloadDelta) => {
    if (downloadDelta.state && downloadDelta.state.current === 'complete') {
        console.log('تم إكمال التحميل:', downloadDelta.id);
        // يمكن إضافة إشعار هنا
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            title: 'Med YouTube Downloader',
            message: 'تم تحميل الفيديو بنجاح!'
        });
    } else if (downloadDelta.state && downloadDelta.state.current === 'interrupted') {
        console.log('فشل التحميل:', downloadDelta.id);
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            title: 'Med YouTube Downloader',
            message: 'فشل في تحميل الفيديو'
        });
    }
});
