@echo off
chcp 65001 >nul
title Med YouTube Downloader Pro - Auto Launcher

echo.
echo ========================================
echo    Med YouTube Downloader Pro
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من:
    echo https://python.org/downloads/
    echo.
    echo ⚠️ تأكد من تفعيل "Add Python to PATH" أثناء التثبيت
    pause
    exit /b 1
)

echo ✅ Python متوفر

REM التحقق من وجود yt-dlp
python -c "import yt_dlp" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبة yt-dlp غير مثبتة
    echo.
    echo 📦 جاري تثبيت yt-dlp...
    pip install yt-dlp
    if errorlevel 1 (
        echo ❌ فشل تثبيت yt-dlp
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت yt-dlp بنجاح
)

echo ✅ yt-dlp متوفر

REM التحقق من وجود ملف البرنامج
if not exist "youtube_downloader.py" (
    echo ❌ ملف youtube_downloader.py غير موجود
    echo.
    echo 📁 تأكد من وجود الملف في نفس مجلد هذا الملف
    pause
    exit /b 1
)

echo ✅ ملف البرنامج موجود
echo.

REM قراءة رابط الفيديو من ملف مؤقت إذا كان موجوداً
set "VIDEO_URL="
if exist "temp_video_url.txt" (
    set /p VIDEO_URL=<temp_video_url.txt
    del temp_video_url.txt >nul 2>&1
    echo 🔗 تم العثور على رابط فيديو: !VIDEO_URL!
    echo.
)

REM تشغيل البرنامج
echo 🚀 تشغيل برنامج التحميل...
echo.

if defined VIDEO_URL (
    REM إذا كان هناك رابط، احصل على الصيغ أولاً
    echo 🔍 جاري الحصول على صيغ الفيديو...
    python youtube_downloader.py --url "%VIDEO_URL%" --get-formats > temp_formats.json 2>&1
    if errorlevel 1 (
        echo ⚠️ فشل في الحصول على الصيغ، سيتم فتح الواجهة العادية
        python youtube_downloader.py --gui
    ) else (
        echo ✅ تم الحصول على الصيغ، فتح الواجهة...
        python youtube_downloader.py --url "%VIDEO_URL%" --format-data-file temp_formats.json --gui
        del temp_formats.json >nul 2>&1
    )
) else (
    REM فتح الواجهة العادية
    python youtube_downloader.py --gui
)

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo 🔧 نصائح لحل المشكلة:
    echo   • تأكد من اتصال الإنترنت
    echo   • جرب تشغيل البرنامج كمدير
    echo   • تحقق من صحة رابط الفيديو
    echo.
) else (
    echo.
    echo ✅ تم إغلاق البرنامج بنجاح
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
