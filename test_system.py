#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Med YouTube Downloader Pro - System Test
اختبار شامل للنظام والمتطلبات
"""

import sys
import subprocess
import importlib
import platform
import os
from pathlib import Path

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*50)
    print(f"🔍 {title}")
    print("="*50)

def check_python():
    """فحص Python"""
    print_header("فحص Python")
    
    print(f"إصدار Python: {sys.version}")
    print(f"المنصة: {platform.platform()}")
    print(f"المعمارية: {platform.architecture()[0]}")
    
    if sys.version_info >= (3, 7):
        print("✅ إصدار Python متوافق")
        return True
    else:
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False

def check_modules():
    """فحص المكتبات المطلوبة"""
    print_header("فحص المكتبات")
    
    required_modules = {
        'yt_dlp': 'yt-dlp',
        'requests': 'requests',
        'tkinter': 'tkinter (مدمج مع Python)',
        'json': 'json (مدمج)',
        'subprocess': 'subprocess (مدمج)',
        'threading': 'threading (مدمج)'
    }
    
    all_available = True
    
    for module, description in required_modules.items():
        try:
            importlib.import_module(module)
            print(f"✅ {description}")
        except ImportError:
            print(f"❌ {description} - غير متوفر")
            all_available = False
    
    return all_available

def check_yt_dlp():
    """فحص yt-dlp بشكل مفصل"""
    print_header("فحص yt-dlp")
    
    try:
        import yt_dlp
        print(f"✅ yt-dlp متوفر - الإصدار: {yt_dlp.version.__version__}")
        
        # اختبار استخراج معلومات فيديو تجريبي
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll - فيديو تجريبي شهير
        
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            try:
                info = ydl.extract_info(test_url, download=False)
                print(f"✅ اختبار استخراج المعلومات نجح")
                print(f"   العنوان: {info.get('title', 'غير معروف')[:50]}...")
                print(f"   عدد الصيغ: {len(info.get('formats', []))}")
                return True
            except Exception as e:
                print(f"⚠️ اختبار استخراج المعلومات فشل: {str(e)[:100]}...")
                print("   (قد يكون بسبب اتصال الإنترنت)")
                return True  # نعتبره نجاح جزئي
                
    except ImportError:
        print("❌ yt-dlp غير مثبت")
        print("   قم بتثبيته: pip install yt-dlp")
        return False
    except Exception as e:
        print(f"❌ خطأ في yt-dlp: {e}")
        return False

def check_files():
    """فحص الملفات المطلوبة"""
    print_header("فحص الملفات")
    
    required_files = [
        'youtube_downloader.py',
        'manifest.json',
        'content.js',
        'background.js',
        'styles.css',
        'popup.html',
        'popup.js',
        'requirements.txt'
    ]
    
    all_present = True
    
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size:,} bytes)")
        else:
            print(f"❌ {file} - غير موجود")
            all_present = False
    
    return all_present

def test_gui():
    """اختبار الواجهة الرسومية"""
    print_header("اختبار الواجهة الرسومية")
    
    try:
        import tkinter as tk
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.title("اختبار الواجهة")
        root.geometry("300x200")
        
        label = tk.Label(root, text="✅ الواجهة الرسومية تعمل!", font=('Arial', 14))
        label.pack(expand=True)
        
        # إغلاق النافذة تلقائياً بعد ثانيتين
        root.after(2000, root.destroy)
        root.mainloop()
        
        print("✅ الواجهة الرسومية تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الواجهة الرسومية: {e}")
        return False

def test_downloader_script():
    """اختبار سكريپت التحميل"""
    print_header("اختبار سكريپت التحميل")
    
    if not os.path.exists('youtube_downloader.py'):
        print("❌ ملف youtube_downloader.py غير موجود")
        return False
    
    try:
        # اختبار تشغيل السكريپت مع --help
        result = subprocess.run([
            sys.executable, 'youtube_downloader.py', '--help'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ سكريپت التحميل يعمل بشكل صحيح")
            return True
        else:
            print(f"❌ خطأ في تشغيل السكريپت: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ انتهت مهلة اختبار السكريپت")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار السكريپت: {e}")
        return False

def generate_report(results):
    """إنشاء تقرير النتائج"""
    print_header("تقرير النتائج النهائي")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"📊 إجمالي الاختبارات: {total_tests}")
    print(f"✅ نجح: {passed_tests}")
    print(f"❌ فشل: {total_tests - passed_tests}")
    print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 تفاصيل النتائج:")
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return True
    else:
        print("\n⚠️ بعض الاختبارات فشلت. راجع التفاصيل أعلاه.")
        print("\n💡 نصائح لحل المشاكل:")
        
        if not results.get('المكتبات'):
            print("   • قم بتشغيل: pip install -r requirements.txt")
        
        if not results.get('الملفات'):
            print("   • تأكد من تحميل جميع ملفات الإضافة")
        
        if not results.get('yt-dlp'):
            print("   • قم بتشغيل: pip install --upgrade yt-dlp")
        
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 Med YouTube Downloader Pro - اختبار النظام")
    print("="*60)
    
    # تشغيل جميع الاختبارات
    results = {
        'Python': check_python(),
        'المكتبات': check_modules(),
        'yt-dlp': check_yt_dlp(),
        'الملفات': check_files(),
        'الواجهة الرسومية': test_gui(),
        'سكريپت التحميل': test_downloader_script()
    }
    
    # إنشاء التقرير
    success = generate_report(results)
    
    print("\n" + "="*60)
    if success:
        print("🎯 النظام جاهز! يمكنك الآن استخدام الإضافة.")
        print("\n📖 للبدء:")
        print("   1. ثبّت الإضافة في Chrome")
        print("   2. اذهب إلى YouTube وانقر زر 'med'")
        print("   3. اختر الصيغة المطلوبة")
    else:
        print("🔧 يرجى حل المشاكل المذكورة أعلاه قبل الاستخدام.")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
